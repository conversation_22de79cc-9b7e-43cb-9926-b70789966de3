import json
from .probability_compare_dataset import ProbabilityCompareDataset
from framework import data_structures, utils
from typing import Optional
import os
import re
import numpy as np
from .probability_compare_dataset import ProbabilityCompareTest


class SciQ_dataset:
    URL = None

    def __init__(self, vocabulary: data_structures.vocabulary.Vocabulary, cache_dir: str = "./cache") -> None:
        self.cache_dir = f"{cache_dir}/{self.__class__.__name__}/"
        os.makedirs(self.cache_dir, exist_ok=True)

        self.vocabulary = vocabulary
        if len(self.vocabulary) <= 256:
            self.dtype = np.uint8
        if len(self.vocabulary) < 32768:
            self.dtype = np.int16
        else:
            self.dtype = np.int32

        self.data = []

        self.load_dataset()
        self.maxlen = max(d["max_length"] for d in self.data)

    def __len__(self):
        return len(self.data)
    
    def load_dataset(self):
        with open(f"{self.cache_dir}data/test_shuffle.json", "r") as f:
            for line in f:
                question = line["question"]
                context = line["context"]
                options = line["options"]
                answer = line["answer_index"]

                ctx = self.vocabulary.sentence_to_indices("Context: " + context + "\nQuestion: " + question + "\nAnswer:")
                endings = [self.vocabulary.sentence_to_indices(" " + e) for e in options]

                options = [ctx + endings[answer]]
                for i, e in enumerate(endings):
                    if i != answer:
                        options.append(ctx + e)

                self.data.append({
                    "options": options,
                    "max_length": max(len(i) for i in options),
                    "prefix_length": len(ctx)
                })

    def __getitem__(self, idx):
        data = self.data[idx]

        res = {
            "sentence_good": np.array(data["options"][0], dtype=self.dtype),
            "good_len": len(data["options"][0]),
            "prefix_len": data["prefix_length"],
            "max_length": data["max_length"],
            "group": 0
        }

        for i, d in enumerate(data["options"][1:]):
            res[f"sentence_bad_{i}"] = np.array(d, dtype=self.dtype)
            res[f"bad_len_{i}"] = len(d)

        return res

    def start_test(self):
        return ProbabilityCompareTest(["test"], n_ways=4, normalize_by_length=True)