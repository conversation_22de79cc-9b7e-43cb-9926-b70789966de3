import numpy as np
import matplotlib.pyplot as plt

np.load("/home/<USER>/moeut_training_code/paper/deepseek/router_saturation_679M/smoe/model-20000.npy").shape

# Data: steps (in thousands) and corresponding PPL values for each method.
steps = np.array([10, 20, 30])
smoe = np.array([20.62, 17.83, 16.53])
deepseek = np.array([20.50, 17.67, 16.31])
smoe_norm = np.array([20.63, 17.87, 16.57])
deepseek_shared = np.array([20.44, 17.65, 16.39])

# Create the figure and plot each method with a distinct marker and line style.
plt.figure(figsize=(12, 10))
plt.plot(steps, smoe, marker='o', linestyle='-', linewidth=2, markersize=4, label='SMoE', color='blue')
plt.plot(steps, deepseek, marker='s', linestyle='-', linewidth=2, markersize=4, label='DeepSeek', color='red')
plt.plot(steps, smoe_norm, marker='^', linestyle='--', linewidth=1, markersize=4, label='SMoE + Norm Sigmoid Router Only', color='orange')
plt.plot(steps, deepseek_shared, marker='d', linestyle='--', linewidth=1, markersize=4, label='DeepSeek Shared Only', color='green')

# Adding labels and title with increased font sizes for clarity.
plt.xlabel('Steps (k)', fontsize=14)
plt.ylabel('Perplexity (PPL)', fontsize=14)
plt.title('Perplexity Performance Comparison (Acc.)', fontsize=16)

# Enhance the grid and ticks for a publication-quality look.
plt.grid(True, linestyle='--', alpha=0.6)
plt.xticks(steps, fontsize=12)
plt.yticks(fontsize=12)
# plt.legend(fontsize=12)

#  save fig with pdf format (no loss of quality and no image borders), save dir: /home/<USER>/moeut_training_code/tmp
# plt.savefig('/home/<USER>/moeut_training_code/tmp/all_ppl_small.pdf', format='pdf', bbox_inches='tight')

plt.savefig('/home/<USER>/moeut_training_code/tmp/all_ppl_small.svg', format='svg', bbox_inches='tight')

plt.tight_layout()
plt.show()


# Data: steps (in thousands) and corresponding PPL values for each method.
steps = np.array([10, 20, 30, 40, 50, 60, 70, 80, 90, 100])
smoe = np.array([20.62, 17.83, 16.53, 15.74, 15.05, 14.57, 14.20, 13.88, 13.72, 13.63])
deepseek = np.array([20.50, 17.67, 16.31, 15.55, 14.84, 14.37, 14.00, 13.69, 13.52, 13.42])
smoe_norm = np.array([20.63, 17.87, 16.57, 15.72, 15.05, 14.54, 14.18, 13.87, 13.70, 13.61])
deepseek_shared = np.array([20.44, 17.65, 16.39, 15.59, 14.93, 14.44, 14.06, 13.76, 13.60, 13.49])

# Create the figure and plot each method with a distinct marker and line style.
plt.figure(figsize=(16, 10))
plt.plot(steps, smoe, marker='o', linestyle='-', linewidth=2, markersize=4, label='SMoE', color='blue')
# plt.plot(steps, deepseek, marker='s', linestyle='-', linewidth=2, markersize=4, label='DeepSeek', color='red')
# plt.plot(steps, smoe_norm, marker='^', linestyle='--', linewidth=1, markersize=4, label='SMoE + Norm Sigmoid Router Only', color='orange')
plt.plot(steps, deepseek_shared, marker='d', linestyle='-', linewidth=1, markersize=4, label='DeepSeek Shared Only', color='green')

# Adding labels and title with increased font sizes for clarity.
plt.xlabel('Steps (k)', fontsize=14)
plt.ylabel('Perplexity (PPL)', fontsize=14)
plt.title('Perplexity Performance Comparison (Acc.)', fontsize=16)

# Enhance the grid and ticks for a publication-quality look.
plt.grid(True, linestyle='--', alpha=0.6)
plt.xticks(steps, fontsize=12)
plt.yticks(fontsize=12)
plt.legend(fontsize=12, loc='lower left')

#  save fig with pdf format (no loss of quality and no image borders), save dir: /home/<USER>/moeut_training_code/tmp
plt.savefig('/home/<USER>/moeut_training_code/tmp/all_ppl.pdf', format='pdf', bbox_inches='tight')

# save fig with svg formate (no loss of quality and no image borders)
plt.savefig('/home/<USER>/moeut_training_code/tmp/all_ppl.svg', format='svg', bbox_inches='tight')
    

plt.tight_layout()
plt.show()


# Data: steps (in thousands) and corresponding percentage values for each method.
steps = np.array([10, 20, 30, 40, 50,
                 60, 70, 80, 90, 100])
smoe = np.array([38.42, 39.26, 40.05, 40.44, 40.79,
                 40.72, 40.99, 41.40, 41.43, 41.35])

deepseek = np.array([38.11, 39.21, 40.04, 40.66, 40.94,
                     40.95, 40.94, 41.61, 41.53, 41.61])

deepseek_shared = np.array([38.53, 39.47, 40.11, 40.45, 40.61,
                        40.73, 40.94, 41.35, 41.47, 41.54])

plt.figure(figsize=(10, 6))
plt.plot(steps, smoe, marker='o', linestyle='-', linewidth=2, markersize=4, label='SMoE', color='blue')
# plt.plot(steps, deepseek, marker='s', linestyle='-', linewidth=2, markersize=4, label='DeepSeek', color='red')
# plt.plot(steps, smoe_norm, marker='^', linestyle='--', linewidth=1, markersize=4, label='SMoE + Norm Sigmoid Router Only', color='orange')
plt.plot(steps, deepseek_shared, marker='d', linestyle='-', linewidth=2, markersize=4, label='DeepSeek Shared Only', color='green')

# Adding labels and title with increased font sizes for clarity.
plt.xlabel('Steps (k)', fontsize=14)
plt.ylabel('Percentage (%)', fontsize=14)
# plt.title('Average Performance Comparison (Percentage): SMoE and DeepSeek Shared Only', fontsize=16)

# Enhance the grid and ticks for a publication-quality look.
plt.grid(True, linestyle='--', alpha=0.2)
plt.xticks([20, 40, 60, 80, 100], fontsize=12)
plt.yticks(fontsize=12)
plt.legend(fontsize=12)

#  save fig with pdf format (no loss of quality and no image borders), save dir: /home/<USER>/moeut_training_code/tmp
plt.savefig('/home/<USER>/moeut_training_code/tmp/shared_smoe_avg_159M.pdf', format='pdf', bbox_inches='tight')
# plt.savefig('/home/<USER>/moeut_training_code/tmp/all_avg.svg', format='svg', bbox_inches='tight')

plt.tight_layout()
plt.show()

# Data: steps (in thousands) and corresponding percentage values for each method.
steps = np.array([20, 40, 60, 80, 100, 120, 140, 160, 180, 200,
                 220, 240, 260, 280, 300, 320, 340, 360, 380, 400])
smoe = np.array([41.90, 43.67, 44.22, 44.77, 45.49, 45.94, 45.97, 46.25, 46.71, 46.86,
                 46.80, 47.15, 47.33, 47.67, 47.18, 47.43, 48.01, 47.84, 47.89, 47.71])
deepseek = np.array([42.07, 43.71, 44.49, 45.00, 45.54, 46.10, 46.00, 46.43, 46.83, 47.23,
                     47.16, 47.17, 47.51, 47.83, 47.74, 48.01, 48.34, 47.94, 48.30, 48.29])
deepseek_shared = np.array([42.13, 43.74, 44.66, 44.58, 45.49, 46.00, 45.99, 46.07, 46.73,46.87, 
                            46.93, 47.03, 47.08, 47.58, 47.45, 47.73, 48.16, 47.87, 48.10, 47.91])

plt.figure(figsize=(6, 4))
plt.plot(steps, smoe, marker='o', linestyle='-', linewidth=2.5, markersize=4, label='SMoE', color='#4bc0d8')
plt.plot(steps, deepseek, marker='s', linestyle='-', linewidth=2.5, markersize=4, label='DeepSeek', color='#e85b9c')
# plt.plot(steps, smoe_norm, marker='^', linestyle='--', linewidth=1, markersize=4, label='SMoE + Norm Sigmoid Router Only', color='orange')
# plt.plot(steps, deepseek_shared, marker='d', linestyle='-', linewidth=2, markersize=4, label='DeepSeek Shared Only', color='green')

# Adding labels and title with increased font sizes for clarity.
plt.xlabel('Training Steps (k)', fontsize=14)
plt.ylabel('Percentage (%)', fontsize=14)
# plt.title('Average Performance Comparison (Percentage): SMoE and DeepSeek Shared Only', fontsize=16)

# Enhance the grid and ticks for a publication-quality look.
plt.grid(True, linestyle='--', alpha=0.2)
plt.xticks([40, 80, 120, 160, 200, 240, 280, 320, 360, 400], fontsize=12)
plt.yticks(fontsize=12)
plt.legend(fontsize=12)

#  save fig with pdf format (no loss of quality and no image borders), save dir: /home/<USER>/moeut_training_code/tmp
plt.savefig('/home/<USER>/moeut_training_code/tmp/deepseek_smoe_avg_679M.pdf', format='pdf', bbox_inches='tight')
# plt.savefig('/home/<USER>/moeut_training_code/tmp/all_avg.svg', format='svg', bbox_inches='tight')

plt.tight_layout()
plt.show()

# Data: steps (in thousands) and corresponding percentage values for each method.
steps = np.array([20, 40, 60, 80, 100, 120, 140, 160, 180, 200,
                 220, 240, 260, 280, 300, 320, 340, 360, 380, 400])
smoe = np.array([41.90, 43.67, 44.22, 44.77, 45.49, 45.94, 45.97, 46.25, 46.71, 46.86,
                 46.80, 47.15, 47.33, 47.67, 47.18, 47.43, 48.01, 47.84, 47.89, 47.71])
deepseek = np.array([42.07, 43.71, 44.49, 45.00, 45.54, 46.10, 46.00, 46.43, 46.83, 47.23,
                     47.16, 47.17, 47.51, 47.83, 47.74, 48.01, 48.34, 47.94, 48.30, 48.29])
deepseek_shared = np.array([
    42.13, 43.74, 44.66, 44.58, 45.49, 46.00, 45.99, 46.07, 46.73, 46.87,
    46.93, 47.03, 47.08, 47.58, 47.45, 47.73, 48.16, 47.87, 48.10, 47.91
])

plt.figure(figsize=(10, 6))
plt.plot(steps, smoe, marker='o', linestyle='-', linewidth=2, markersize=4, label='SMoE', color='blue')
# plt.plot(steps, deepseek, marker='s', linestyle='-', linewidth=2, markersize=4, label='DeepSeek', color='red')
# plt.plot(steps, smoe_norm, marker='^', linestyle='--', linewidth=1, markersize=4, label='SMoE + Norm Sigmoid Router Only', color='orange')
plt.plot(steps, deepseek_shared, marker='d', linestyle='-', linewidth=2, markersize=4, label='DeepSeek Shared Only', color='green')

# Adding labels and title with increased font sizes for clarity.
plt.xlabel('Steps (k)', fontsize=14)
plt.ylabel('Percentage (%)', fontsize=14)
# plt.title('Average Performance Comparison (Percentage): SMoE and DeepSeek Shared Only', fontsize=16)

# Enhance the grid and ticks for a publication-quality look.
plt.grid(True, linestyle='--', alpha=0.2)
plt.xticks([40, 80, 120, 160, 200, 240, 280, 320, 360, 400], fontsize=12)
plt.yticks(fontsize=12)
plt.legend(fontsize=12)

#  save fig with pdf format (no loss of quality and no image borders), save dir: /home/<USER>/moeut_training_code/tmp
plt.savefig('/home/<USER>/moeut_training_code/tmp/shared_smoe_avg_679M.pdf', format='pdf', bbox_inches='tight')
# plt.savefig('/home/<USER>/moeut_training_code/tmp/all_avg.svg', format='svg', bbox_inches='tight')

plt.tight_layout()
plt.show()

colors = {
    "smoe": "#4bc0d8",
    "deepseek": "#e85b9c",
    "smoe_norm": "#f1b921",
    "deepseek_shared": "#313465",
}

# Data: steps (in thousands) and corresponding percentage values for each method.
steps_158m = np.array([10, 20, 30, 40, 50,
                 60, 70, 80, 90, 100])
smoe_158m = np.array([38.42, 39.26, 40.05, 40.44, 40.79,
                 40.72, 40.99, 41.40, 41.43, 41.35])

deepseek_158m = np.array([38.11, 39.21, 40.04, 40.66, 40.94,
                     40.95, 40.94, 41.61, 41.53, 41.61])

deepseek_shared_158m = np.array([38.53, 39.47, 40.11, 40.45, 40.61,
                        40.73, 40.94, 41.35, 41.47, 41.54])

# Data: steps (in thousands) and corresponding percentage values for each method.
steps_679m = np.array([20, 40, 60, 80, 100, 120, 140, 160, 180, 200,
                 220, 240, 260, 280, 300, 320, 340, 360, 380, 400])
smoe_679m = np.array([41.90, 43.67, 44.22, 44.77, 45.49, 45.94, 45.97, 46.25, 46.71, 46.86,
                 46.80, 47.15, 47.33, 47.67, 47.18, 47.43, 48.01, 47.84, 47.89, 47.71])
deepseek_679m = np.array([42.07, 43.71, 44.49, 45.00, 45.54, 46.10, 46.00, 46.43, 46.83, 47.23,
                     47.16, 47.17, 47.51, 47.83, 47.74, 48.01, 48.34, 47.94, 48.30, 48.29])
deepseek_shared_679m = np.array([42.13, 43.74, 44.66, 44.58, 45.49, 46.00, 45.99, 46.07, 46.73,46.87, 
                            46.93, 47.03, 47.08, 47.58, 47.45, 47.73, 48.16, 47.87, 48.10, 47.91])

fig, axs = plt.subplots(2, 2, figsize=(20, 11), sharex=False, sharey=False)

# Set shared labels
fig.text(0.5, 0.08, 'Training Steps (k)', ha='center', fontsize=25, weight='bold')
fig.text(0.04, 0.55, 'Avg Performance (%)', va='center', rotation='vertical', fontsize=25, weight='bold')

xlims = [(7.5, 102.5), (0, 410)]
xticks = [np.arange(20, 101, 20)] + [np.arange(80, 401, 80)]
ylims = [(38, 42), (41, 49)]
yticks = [np.arange(38, 43, 1)] + [np.arange(41, 50, 2)]

# For legend collection
lines = []
labels = []

for i, ax in enumerate(axs):
    if i == 0:
        l1, = ax[0].plot(steps_158m, smoe_158m, linewidth=4, color='#4bc0d8', marker='o', markersize=6)
        l2, = ax[0].plot(steps_158m, deepseek_158m, linewidth=4, color='#e85b9c', marker='s', markersize=6)
        lines.extend([l1, l2])
        labels.extend(["SMoE", "DeepSeek-V3"])
        ax[0].set_title("158M", fontsize=25, weight='bold')
        
        l3, = ax[1].plot(steps_158m, smoe_158m, linewidth=4, color='#4bc0d8', marker='o', markersize=6)
        l4, = ax[1].plot(steps_158m, deepseek_shared_158m, linewidth=4, color='#313465', marker='h', markersize=6)
        lines.extend([l4])
        labels.extend(["DeepSeek-V2"])
        ax[1].set_title("158M", fontsize=25, weight='bold')

    elif i == 1:
        ax[0].plot(steps_679m, smoe_679m, linewidth=4, color='#4bc0d8', marker='o', markersize=6)
        ax[0].plot(steps_679m, deepseek_679m, linewidth=4, color='#e85b9c', marker='s', markersize=6)
        ax[0].set_title("679M", fontsize=25, weight='bold')

        ax[1].plot(steps_679m, smoe_679m, linewidth=4, color='#4bc0d8', marker='o', markersize=6)
        ax[1].plot(steps_679m, deepseek_shared_679m, linewidth=4, color='#313465', marker='h', markersize=6)
        ax[1].set_title("679M", fontsize=25, weight='bold')
        

    ax[0].set_xticks(xticks[i])
    ax[0].set_xlim(xlims[i])
    ax[1].set_xticks(xticks[i])
    ax[1].set_xlim(xlims[i])
    
    ax[0].set_yticks(yticks[i])
    ax[0].set_ylim(ylims[i])
    ax[1].set_yticks(yticks[i])
    ax[1].set_ylim(ylims[i])
    
    ax[0].xaxis.set_tick_params(labelsize=25)
    ax[0].yaxis.set_tick_params(labelsize=25)
    ax[1].xaxis.set_tick_params(labelsize=25)
    ax[1].yaxis.set_tick_params(labelsize=25)
    
    ax[0].spines['right'].set_visible(False)
    ax[0].spines['top'].set_visible(False)
    ax[1].spines['right'].set_visible(False)
    ax[1].spines['top'].set_visible(False)

# Create a global legend in the right
fig.legend(lines, labels, loc='lower center', ncol=4, fontsize=20)

plt.tight_layout(rect=[0.06, 0.1, 1, 1])  # adjust bottom for legend space
plt.savefig('/home/<USER>/moeut_training_code/paper/deepseek/results/performances/lm/lm_performance.pdf', format='pdf', bbox_inches='tight')
plt.show()

colors = {
    "smoe": "#4bc0d8",
    "deepseek": "#e85b9c",
    "smoe_norm": "#f1b921",
    "deepseek_shared": "#313465",
}

# Data: steps (in thousands) and corresponding percentage values for each method.
steps_158m = np.array([10, 20, 30, 40, 50, 60, 70, 80, 90, 100])
smoe_158m = np.array([39.85, 40.56, 41.53, 41.91, 42.16, 42.25, 42.57, 42.74, 42.92, 42.90])
smoe_sigmoid_158m = np.array([39.55, 40.47, 41.32, 41.86, 42.05, 42.43, 42.61, 42.90, 42.95, 43.04])
deepseek_158m = np.array([39.55, 40.50, 41.40, 42.11, 42.42, 42.46, 42.55, 43.24, 43.12, 43.21])
deepseek_shared_158m = np.array([39.85, 40.88, 41.50, 41.94, 42.19, 42.15, 42.51, 42.85, 42.86, 43.18])

# Data: steps (in thousands) and corresponding percentage values for each method.
steps_679m = np.array([20, 40, 60, 80, 100, 120, 140, 160, 180, 200,
                 220, 240, 260, 280, 300, 320, 340, 360, 380, 400])
smoe_679m = np.array([41.90, 43.67, 44.22, 44.77, 45.49, 45.94, 45.97, 46.25, 46.71, 46.86,
                 46.80, 47.15, 47.33, 47.67, 47.18, 47.43, 48.01, 47.84, 47.89, 47.71])
smoe_sigmoid_679m = np.array([42.25, 43.78, 44.38, 44.97, 45.55, 46.14, 46.24, 46.18, 47.01, 47.12,
                 47.06, 47.43, 47.21, 47.64, 47.23, 48.03, 48.23, 48.21, 48.23, 48.06])
deepseek_679m = np.array([42.07, 43.71, 44.49, 45.00, 45.54, 46.10, 46.00, 46.43, 46.83, 47.23,
                     47.16, 47.17, 47.51, 47.83, 47.74, 48.01, 48.34, 47.94, 48.30, 48.29])
deepseek_shared_679m = np.array([42.13, 43.74, 44.66, 44.58, 45.49, 46.00, 45.99, 46.07, 46.73,46.87, 
                            46.93, 47.03, 47.08, 47.58, 47.45, 47.73, 48.16, 47.87, 48.10, 47.91])

fig, axs = plt.subplots(2, 3, figsize=(20, 9), sharex=False, sharey=False)

# Set shared labels
fig.text(0.55, 0.0, 'Training Steps (k)', ha='center', fontsize=25, weight='bold')
fig.text(0.04, 0.55, 'Avg Performance (%)', va='center', rotation='vertical', fontsize=25, weight='bold')

fig.text(1, 0.78, '158M', va='center', rotation='vertical', fontsize=30, weight='bold')
fig.text(1, 0.32, '679M', va='center', rotation='vertical', fontsize=30, weight='bold')


xlims = [(7.5, 102.5), (0, 410)]
xticks = [np.arange(20, 101, 20)] + [np.arange(80, 401, 80)]
ylims = [(39.5, 43.5), (41, 49)]
yticks = [np.arange(39, 44, 1)] + [np.arange(41, 50, 2)]

# For legend collection
lines = []
labels = []

for i, ax in enumerate(axs):
    if i == 0:
        l1, = ax[0].plot(steps_158m, smoe_158m, linewidth=4, color='#4bc0d8', marker='o', markersize=6)
        l2, = ax[0].plot(steps_158m, deepseek_158m, linewidth=4, color='#e85b9c', marker='s', markersize=6)
        lines.extend([l1, l2])
        labels.extend(["SMoE", "DeepSeek-V3"])
        # ax[0].set_title("158M", fontsize=25, weight='bold')
        
        l3, = ax[1].plot(steps_158m, smoe_158m, linewidth=4, color='#4bc0d8', marker='o', markersize=6)
        l4, = ax[1].plot(steps_158m, deepseek_shared_158m, linewidth=4, color='#313465', marker='h', markersize=6)
        lines.extend([l4])
        labels.extend(["DeepSeek-V2"])
        # ax[1].set_title("158M", fontsize=25, weight='bold')
        
        l5, = ax[2].plot(steps_158m, smoe_158m, linewidth=4, color='#4bc0d8', marker='o', markersize=6)
        l6, = ax[2].plot(steps_158m, smoe_sigmoid_158m, linewidth=4, color='#f1b921', marker='s', markersize=6)
        lines.extend([l6])
        labels.extend(["SMoE Sigmoid Gating"])
        # ax[2].set_title("158M", fontsize=25, weight='bold')

    elif i == 1:
        ax[0].plot(steps_679m, smoe_679m, linewidth=4, color='#4bc0d8', marker='o', markersize=6)
        ax[0].plot(steps_679m, deepseek_679m, linewidth=4, color='#e85b9c', marker='s', markersize=6)
        # ax[0].set_title("679M", fontsize=25, weight='bold')

        ax[1].plot(steps_679m, smoe_679m, linewidth=4, color='#4bc0d8', marker='o', markersize=6)
        ax[1].plot(steps_679m, deepseek_shared_679m, linewidth=4, color='#313465', marker='h', markersize=6)
        # ax[1].set_title("679M", fontsize=25, weight='bold')
        
        ax[2].plot(steps_679m, smoe_679m, linewidth=4, color='#4bc0d8', marker='o', markersize=6)
        ax[2].plot(steps_679m, smoe_sigmoid_679m, linewidth=4, color='#f1b921', marker='s', markersize=6)
        # ax[2].set_title("679M", fontsize=25, weight='bold')

    ax[0].set_xticks(xticks[i])
    ax[0].set_xlim(xlims[i])
    ax[1].set_xticks(xticks[i])
    ax[1].set_xlim(xlims[i])
    ax[2].set_xticks(xticks[i])
    ax[2].set_xlim(xlims[i])
    
    ax[0].set_yticks(yticks[i])
    ax[0].set_ylim(ylims[i])
    ax[1].set_yticks(yticks[i])
    ax[1].set_ylim(ylims[i])
    ax[2].set_yticks(yticks[i])
    ax[2].set_ylim(ylims[i])
    
    ax[0].xaxis.set_tick_params(labelsize=25)
    ax[0].yaxis.set_tick_params(labelsize=25)
    ax[1].xaxis.set_tick_params(labelsize=25)
    ax[1].yaxis.set_tick_params(labelsize=25)
    ax[2].xaxis.set_tick_params(labelsize=25)
    ax[2].yaxis.set_tick_params(labelsize=25)
    
    ax[0].spines['right'].set_visible(False)
    ax[0].spines['top'].set_visible(False)
    ax[1].spines['right'].set_visible(False)
    ax[1].spines['top'].set_visible(False)
    ax[2].spines['right'].set_visible(False)
    ax[2].spines['top'].set_visible(False)

# Create a global legend in the right
fig.legend(lines, labels, loc='upper center', ncol=4, fontsize=20, bbox_to_anchor=(0.55, 1.0),)

plt.tight_layout(rect=[0.06, 0.03, 1, 0.93])  # leave top 8% blank
plt.savefig('/home/<USER>/moeut_training_code/paper/deepseek/results/performances/lm/lm_performance_pair.pdf', format='pdf', bbox_inches='tight')
plt.show()

colors = {
    "smoe": "#4bc0d8",
    "deepseek": "#e85b9c",
    "smoe_norm": "#f1b921",
    "deepseek_shared": "#313465",
}

# Data: steps (in thousands) and corresponding percentage values for each method.
steps_158m = np.array([10, 20, 30, 40, 50, 60, 70, 80, 90, 100])
smoe_158m = np.array([39.85, 40.56, 41.53, 41.91, 42.16, 42.25, 42.57, 42.74, 42.92, 42.90])
smoe_sigmoid_158m = np.array([39.55, 40.47, 41.32, 41.86, 42.05, 42.43, 42.61, 42.90, 42.95, 43.04])
deepseek_158m = np.array([39.55, 40.50, 41.40, 42.11, 42.42, 42.46, 42.55, 43.24, 43.12, 43.21])
deepseek_shared_158m = np.array([39.85, 40.88, 41.50, 41.94, 42.19, 42.15, 42.51, 42.85, 42.86, 43.18])

# Data: steps (in thousands) and corresponding percentage values for each method.
steps_679m = np.array([20, 40, 60, 80, 100, 120, 140, 160, 180, 200,
                 220, 240, 260, 280, 300, 320, 340, 360, 380, 400])
smoe_679m = np.array([41.90, 43.67, 44.22, 44.77, 45.49, 45.94, 45.97, 46.25, 46.71, 46.86,
                 46.80, 47.15, 47.33, 47.67, 47.18, 47.43, 48.01, 47.84, 47.89, 47.71])
deepseek_679m = np.array([42.07, 43.71, 44.49, 45.00, 45.54, 46.10, 46.00, 46.43, 46.83, 47.23,
                     47.16, 47.17, 47.51, 47.83, 47.74, 48.01, 48.34, 47.94, 48.30, 48.29])
deepseek_shared_679m = np.array([42.13, 43.74, 44.66, 44.58, 45.49, 46.00, 45.99, 46.07, 46.73,46.87, 
                            46.93, 47.03, 47.08, 47.58, 47.45, 47.73, 48.16, 47.87, 48.10, 47.91])

fig, axs = plt.subplots(1, 2, figsize=(20, 6), sharex=False, sharey=False)

# Set shared labels
fig.text(0.5, 0.13, 'Training Steps (k)', ha='center', fontsize=25, weight='bold')
fig.text(0.04, 0.6, 'Avg Performance (%)', va='center', rotation='vertical', fontsize=25, weight='bold')

# fig.text(0.32, 0.92, '158M', ha='center', fontsize=30, weight='bold')
# fig.text(0.8, 0.92, '679M', ha='center', fontsize=30, weight='bold')

xlims = [(7.5, 102.5), (0, 410)]
xticks = [np.arange(20, 101, 20)] + [np.arange(80, 401, 80)]
ylims = [(39.45, 43.3), (41.8, 48.4)]
yticks = [np.arange(39, 44, 1)] + [np.arange(41, 50, 2)]

# ylims = [(39.5, 43.5), (41, 49)]
# yticks = [np.arange(39, 44, 1)] + [np.arange(41, 50, 2)]
# For legend collection
lines = []
labels = []

l1, = axs[0].plot(steps_158m, smoe_158m, linewidth=4, color='#4bc0d8', marker='o', markersize=6, linestyle='--')
l2, = axs[0].plot(steps_158m, deepseek_shared_158m, linewidth=4, color='#313465', marker='h', markersize=6)
l3, = axs[0].plot(steps_158m, deepseek_158m, linewidth=4, color='#e85b9c', marker='s', markersize=6)

lines.extend([l1, l2, l3])
labels.extend(["SMoE", "DeepSeek-V2", "DeepSeek-V3"])
# axs[0].set_title("158M", fontsize=30, weight='bold')


axs[1].plot(steps_679m, smoe_679m, linewidth=4, color='#4bc0d8', marker='o', markersize=6, linestyle='--')
axs[1].plot(steps_679m, deepseek_shared_679m, linewidth=4, color='#313465', marker='h', markersize=6)
axs[1].plot(steps_679m, deepseek_679m, linewidth=4, color='#e85b9c', marker='s', markersize=6)
# axs[1].set_title("679M", fontsize=30, weight='bold')
        

axs[0].set_xticks(xticks[0])
axs[0].set_xlim(xlims[0])
axs[1].set_xticks(xticks[1])
axs[1].set_xlim(xlims[1])

axs[0].set_yticks(yticks[0])
axs[0].set_ylim(ylims[0])
axs[1].set_yticks(yticks[1])
axs[1].set_ylim(ylims[1])

axs[0].xaxis.set_tick_params(labelsize=25)
axs[0].yaxis.set_tick_params(labelsize=25)
axs[1].xaxis.set_tick_params(labelsize=25)
axs[1].yaxis.set_tick_params(labelsize=25)

axs[0].spines['right'].set_visible(False)
axs[0].spines['top'].set_visible(False)
axs[1].spines['right'].set_visible(False)
axs[1].spines['top'].set_visible(False)

# Create a global legend in the right
fig.legend(lines, labels, loc='lower center', ncol=4, fontsize=20)

plt.tight_layout(rect=[0.06, 0.15, 1, 1])  # adjust bottom for legend space
plt.savefig('/home/<USER>/moeut_training_code/paper/deepseek/results/performances/lm/lm_performance_2sub.pdf', format='pdf', bbox_inches='tight')
plt.show()

axs_gap = plt.twinx()
gap_v2 = deepseek_shared_158m - smoe_158m
gap_v3 = deepseek_158m      - smoe_158m
gap_v4 = smoe_sigmoid_158m - smoe_158m
axs_gap.plot(steps_158m, gap_v2, color=colors['deepseek_shared'], label="V2 - SMoE")
axs_gap.plot(steps_158m, gap_v3, color=colors['deepseek'], label="V3 - SMoE")   
axs_gap.plot(steps_158m, gap_v4, color=colors['smoe_norm'], label="SMoE Sigmoid Gating")
axs_gap.axhline(0, color="k", linewidth=1)
axs_gap.set_ylabel("Δ Avg Perf. (%)")
axs_gap.set_ylim(-0.5, 0.5)
axs_gap.set_yticks([-0.5, 0, 0.5])
plt.legend(loc='upper left')

plt.show()

gap_v3

axs_gap = plt.twinx()
gap_v2 = deepseek_shared_679m - smoe_679m
gap_v3 = deepseek_679m      - smoe_679m
gap_v4 = smoe_sigmoid_679m - smoe_679m
axs_gap.plot(steps_679m, gap_v2, color=colors['deepseek_shared'], label="V2 - SMoE")
axs_gap.plot(steps_679m, gap_v3, color=colors['deepseek'], label="V3 - SMoE")
axs_gap.plot(steps_679m, gap_v4, color=colors['smoe_norm'], label="SMoE Sigmoid Gating")
axs_gap.axhline(0, color="k", linewidth=1)
axs_gap.set_ylabel("Δ Avg Perf. (%)")
axs_gap.set_ylim(-0.5, 0.8)
axs_gap.set_yticks([-0.5, 0, 0.5])
plt.legend(loc='upper left')

plt.show()

gap_v3


colors = {
    "smoe": "#4bc0d8",
    "deepseek": "#e85b9c",
    "smoe_norm": "#f1b921",
    "deepseek_shared": "#313465",
}

# Data: steps (in thousands) and corresponding percentage values for each method.
steps_158m = np.array([10, 20, 30, 40, 50, 60, 70, 80, 90, 100])
smoe_158m = np.array([39.85, 40.56, 41.53, 41.91, 42.16, 42.25, 42.57, 42.74, 42.92, 42.90])
smoe_sigmoid_158m = np.array([39.55, 40.50, 41.40, 42.11, 42.42, 42.46, 42.55, 43.24, 43.12, 43.21])
deepseek_158m = np.array([39.55, 40.50, 41.40, 42.11, 42.42, 42.46, 42.55, 43.24, 43.12, 43.21])
deepseek_shared_158m = np.array([39.85, 40.88, 41.50, 41.94, 42.19, 42.15, 42.51, 42.85, 42.86, 43.18])

# Data: steps (in thousands) and corresponding percentage values for each method.
steps_679m = np.array([20, 40, 60, 80, 100, 120, 140, 160, 180, 200,
                 220, 240, 260, 280, 300, 320, 340, 360, 380, 400])
smoe_679m = np.array([41.90, 43.67, 44.22, 44.77, 45.49, 45.94, 45.97, 46.25, 46.71, 46.86,
                 46.80, 47.15, 47.33, 47.67, 47.18, 47.43, 48.01, 47.84, 47.89, 47.71])
deepseek_679m = np.array([42.07, 43.71, 44.49, 45.00, 45.54, 46.10, 46.00, 46.43, 46.83, 47.23,
                     47.16, 47.17, 47.51, 47.83, 47.74, 48.01, 48.34, 47.94, 48.30, 48.29])
deepseek_shared_679m = np.array([42.13, 43.74, 44.66, 44.58, 45.49, 46.00, 45.99, 46.07, 46.73,46.87, 
                            46.93, 47.03, 47.08, 47.58, 47.45, 47.73, 48.16, 47.87, 48.10, 47.91])

fig, axs = plt.subplots(1, 2, figsize=(20, 6.5), sharex=False, sharey=False)

# Set shared labels
fig.text(0.55, 0.0, 'Training Steps (k)', ha='center', fontsize=27, weight='bold')
fig.text(0.04, 0.5, 'Avg Performance (%)', va='center', rotation='vertical', fontsize=27, weight='bold')

# fig.text(0.32, 0.92, '158M', ha='center', fontsize=30, weight='bold')
# fig.text(0.8, 0.92, '679M', ha='center', fontsize=30, weight='bold')

xlims = [(7.5, 102.5), (0, 410)]
xticks = [np.arange(20, 101, 20)] + [np.arange(80, 401, 80)]
ylims = [(39.45, 43.3), (41.7, 48.4)]
yticks = [np.arange(39, 44, 1)] + [np.arange(41, 50, 2)]

# ylims = [(39.5, 43.5), (41, 49)]
# yticks = [np.arange(39, 44, 1)] + [np.arange(41, 50, 2)]
# For legend collection
lines = []
labels = []

zoom = axs[0].inset_axes([0.63, 0.12, 0.35, 0.45])
zoom.plot(steps_158m, smoe_158m, color=colors['smoe'], linewidth=2.5, linestyle='--')
zoom.plot(steps_158m, deepseek_shared_158m, color=colors['deepseek_shared'], linewidth=3)
zoom.plot(steps_158m, deepseek_158m, color=colors['deepseek'], linewidth=3)
zoom.set_xlim(70, 100); zoom.set_ylim(42.5, 43.26)
zoom.set_xticks([70, 80, 90, 100]); zoom.set_yticks([42.5, 42.7, 42.9, 43.1])
# zoom.axhline(42.9, color="#4bc0d8", linewidth=1, linestyle="--", alpha=0.5)
zoom.xaxis.set_tick_params(labelsize=16)
zoom.yaxis.set_tick_params(labelsize=16)


l1, = axs[0].plot(steps_158m, smoe_158m, linewidth=3, color='#4bc0d8', marker='o', markersize=6, linestyle='--')
l2, = axs[0].plot(steps_158m, deepseek_shared_158m, linewidth=4, color='#313465', marker='h', markersize=6)
l3, = axs[0].plot(steps_158m, deepseek_158m, linewidth=4, color='#e85b9c', marker='s', markersize=6)

# plot a horizontal line at y = 42.9
# axs[0].axhline(42.9, color="#4bc0d8", linewidth=2, linestyle="--", alpha=0.5)

lines.extend([l1, l2, l3])
labels.extend(["SMoE", "DeepSeek-V2", "DeepSeek-V3"])
# axs[0].set_title("158M", fontsize=30, weight='bold')


axs[1].plot(steps_679m, smoe_679m, linewidth=3, color='#4bc0d8', marker='o', markersize=6, linestyle='--')
axs[1].plot(steps_679m, deepseek_shared_679m, linewidth=4, color='#313465', marker='h', markersize=6)
axs[1].plot(steps_679m, deepseek_679m, linewidth=4, color='#e85b9c', marker='s', markersize=6)
# axs[1].set_title("679M", fontsize=30, weight='bold')

# plot a horizontal line at y = 47.7
# axs[1].axhline(47.7, color="#4bc0d8", linewidth=2, linestyle="--", alpha=0.5)

zoom2 = axs[1].inset_axes([0.63, 0.12, 0.35, 0.45])
zoom2.plot(steps_679m, smoe_679m, color=colors['smoe'], linewidth=2.5, linestyle='--')
zoom2.plot(steps_679m, deepseek_shared_679m, color=colors['deepseek_shared'], linewidth=3)
zoom2.plot(steps_679m, deepseek_679m, color=colors['deepseek'], linewidth=3)
zoom2.set_xlim(300, 400); zoom2.set_ylim(47.1, 48.4)
zoom2.set_xticks([300, 320, 340, 360, 380, 400]); zoom2.set_yticks([47.1, 47.4, 47.7, 48, 48.3])
zoom2.xaxis.set_tick_params(labelsize=16)
zoom2.yaxis.set_tick_params(labelsize=16)
# zoom2.axhline(47.7, color="#4bc0d8", linewidth=1, linestyle="--", alpha=0.5)

for spine in ["left", "right", "top", "bottom"]:
    zoom.spines[spine].set_linestyle("--")
    zoom.spines[spine].set_linewidth(0.2)
    zoom.spines[spine].set_alpha(0.2)
    
    zoom2.spines[spine].set_linestyle("--")
    zoom2.spines[spine].set_linewidth(0.2)
    zoom2.spines[spine].set_alpha(0.2)


axs[0].set_xticks(xticks[0])
axs[0].set_xlim(xlims[0])
axs[1].set_xticks(xticks[1])
axs[1].set_xlim(xlims[1])

axs[0].set_yticks(yticks[0])
axs[0].set_ylim(ylims[0])
axs[1].set_yticks(yticks[1])
axs[1].set_ylim(ylims[1])

axs[0].xaxis.set_tick_params(labelsize=25)
axs[0].yaxis.set_tick_params(labelsize=25)
axs[1].xaxis.set_tick_params(labelsize=25)
axs[1].yaxis.set_tick_params(labelsize=25)

axs[0].spines['right'].set_visible(False)
axs[0].spines['top'].set_visible(False)
axs[1].spines['right'].set_visible(False)
axs[1].spines['top'].set_visible(False)

# Create a global legend in the right
# fig.legend(lines, labels, loc='lower center', ncol=4, fontsize=20)
fig.legend(
    lines,
    labels,
    loc='upper center',
    bbox_to_anchor=(0.55, 1.01),
    ncol=3,                        # you only have 3 entries
    fontsize=21.5
)

# plt.tight_layout(rect=[0.06, 0.16, 1, 1])  # adjust bottom for legend space
plt.tight_layout(rect=[0.06, 0.02, 1, 0.92])  # leave top 8% blank
plt.savefig('/home/<USER>/moeut_training_code/paper/deepseek/results/performances/lm/lm_performance.pdf', format='pdf', bbox_inches='tight')
plt.show()

colors = {
    "smoe": "#4bc0d8",
    "deepseek": "#e85b9c",
    "smoe_norm": "#f1b921",
    "deepseek_shared": "#313465",
}

# Data: steps (in thousands) and corresponding percentage values for each method.
steps_158m = np.array([10, 20, 30, 40, 50,
                 60, 70, 80, 90, 100])
smoe_158m = np.array([38.42, 39.26, 40.05, 40.44, 40.79,
                 40.72, 40.99, 41.40, 41.43, 41.35])

deepseek_158m = np.array([38.11, 39.21, 40.04, 40.66, 40.94,
                     40.95, 40.94, 41.61, 41.53, 41.61])

deepseek_shared_158m = np.array([38.53, 39.47, 40.11, 40.45, 40.61,
                        40.73, 40.94, 41.35, 41.47, 41.54])

# Data: steps (in thousands) and corresponding percentage values for each method.
steps_679m = np.array([20, 40, 60, 80, 100, 120, 140, 160, 180, 200,
                 220, 240, 260, 280, 300, 320, 340, 360, 380, 400])
smoe_679m = np.array([41.90, 43.67, 44.22, 44.77, 45.49, 45.94, 45.97, 46.25, 46.71, 46.86,
                 46.80, 47.15, 47.33, 47.67, 47.18, 47.43, 48.01, 47.84, 47.89, 47.71])
deepseek_679m = np.array([42.07, 43.71, 44.49, 45.00, 45.54, 46.10, 46.00, 46.43, 46.83, 47.23,
                     47.16, 47.17, 47.51, 47.83, 47.74, 48.01, 48.34, 47.94, 48.30, 48.29])
deepseek_shared_679m = np.array([42.13, 43.74, 44.66, 44.58, 45.49, 46.00, 45.99, 46.07, 46.73,46.87, 
                            46.93, 47.03, 47.08, 47.58, 47.45, 47.73, 48.16, 47.87, 48.10, 47.91])

fig, axs = plt.subplots(2, 3, figsize=(20, 9), sharex=False, sharey=False)

# Set shared labels
fig.text(0.5, 0.085, 'Training Steps (k)', ha='center', fontsize=25, weight='bold')
fig.text(0.04, 0.55, 'Avg Performance (%)', va='center', rotation='vertical', fontsize=25, weight='bold')

fig.text(1, 0.8, '158M', va='center', rotation='vertical', fontsize=30, weight='bold')
fig.text(1, 0.375, '679M', va='center', rotation='vertical', fontsize=30, weight='bold')


xlims = [(7.5, 102.5), (0, 410)]
xticks = [np.arange(20, 101, 20)] + [np.arange(80, 401, 80)]
ylims = [(38, 41.8), (41.8, 48.5)]
yticks = [np.arange(38, 43, 1)] + [np.arange(41, 50, 1)]


# For legend collection
lines = []
labels = []

for i, ax in enumerate(axs):
    if i == 0:
        l1, = ax[0].plot(steps_158m, smoe_158m, linewidth=4, color='#4bc0d8', marker='o', markersize=6)
        l2, = ax[0].plot(steps_158m, deepseek_158m, linewidth=4, color='#e85b9c', marker='s', markersize=6)
        lines.extend([l1, l2])
        labels.extend(["SMoE", "DeepSeek-V3"])
        # ax[0].set_title("158M", fontsize=25, weight='bold')
        
        l3, = ax[1].plot(steps_158m, smoe_158m, linewidth=4, color='#4bc0d8', marker='o', markersize=6)
        l4, = ax[1].plot(steps_158m, deepseek_shared_158m, linewidth=4, color='#313465', marker='h', markersize=6)
        lines.extend([l4])
        labels.extend(["DeepSeek-V2"])
        # ax[1].set_title("158M", fontsize=25, weight='bold')
        
        # ax[2] is comparision between deepseek_shared and deepseek
        ax[2].plot(steps_158m, smoe_158m, linewidth=2, color='#4bc0d8', marker='o', markersize=4, linestyle='--')
        ax[2].plot(steps_158m, deepseek_158m, linewidth=4, color='#e85b9c', marker='s', markersize=6)
        ax[2].plot(steps_158m, deepseek_shared_158m, linewidth=4, color='#313465', marker='h', markersize=6)
        # ax[2].set_title("158M", fontsize=25, weight='bold')

    elif i == 1:
        ax[0].plot(steps_679m, smoe_679m, linewidth=4, color='#4bc0d8', marker='o', markersize=6)
        ax[0].plot(steps_679m, deepseek_679m, linewidth=4, color='#e85b9c', marker='s', markersize=6)
        # ax[0].set_title("679M", fontsize=25, weight='bold')

        ax[1].plot(steps_679m, smoe_679m, linewidth=4, color='#4bc0d8', marker='o', markersize=6)
        ax[1].plot(steps_679m, deepseek_shared_679m, linewidth=4, color='#313465', marker='h', markersize=6)
        # ax[1].set_title("679M", fontsize=25, weight='bold')
        
        ax[2].plot(steps_679m, smoe_679m, linewidth=2, color='#4bc0d8', marker='o', markersize=4, linestyle='--')
        ax[2].plot(steps_679m, deepseek_679m, linewidth=4, color='#e85b9c', marker='s', markersize=6)
        ax[2].plot(steps_679m, deepseek_shared_679m, linewidth=4, color='#313465', marker='h', markersize=6)
        # ax[2].set_title("679M", fontsize=25, weight='bold')

    ax[0].set_xticks(xticks[i])
    ax[0].set_xlim(xlims[i])
    ax[1].set_xticks(xticks[i])
    ax[1].set_xlim(xlims[i])
    ax[2].set_xticks(xticks[i])
    ax[2].set_xlim(xlims[i])
    
    ax[0].set_yticks(yticks[i])
    ax[0].set_ylim(ylims[i])
    ax[1].set_yticks([])
    ax[1].set_ylim(ylims[i])
    ax[2].set_yticks([])
    ax[2].set_ylim(ylims[i])
    
    ax[0].xaxis.set_tick_params(labelsize=25)
    ax[0].yaxis.set_tick_params(labelsize=25)
    ax[1].xaxis.set_tick_params(labelsize=25)
    ax[1].yaxis.set_tick_params(labelsize=25)
    ax[2].xaxis.set_tick_params(labelsize=25)
    ax[2].yaxis.set_tick_params(labelsize=25)
    
    ax[0].spines['right'].set_visible(False)
    ax[0].spines['top'].set_visible(False)
    ax[1].spines['right'].set_visible(False)
    ax[1].spines['top'].set_visible(False)
    ax[2].spines['right'].set_visible(False)
    ax[2].spines['top'].set_visible(False)

# Create a global legend in the right
fig.legend(lines, labels, loc='lower center', ncol=4, fontsize=20)

plt.tight_layout(rect=[0.06, 0.1, 1, 1])  # adjust bottom for legend space
plt.savefig('/home/<USER>/moeut_training_code/paper/deepseek/results/performances/lm/lm_performance.pdf', format='pdf', bbox_inches='tight')
plt.show()

colors = {
    "smoe": "#4bc0d8",
    "deepseek": "#e85b9c",
    "smoe_norm": "#f1b921",
    "deepseek_shared": "#313465",
}

# Data: steps (in thousands) and corresponding percentage values for each method.
steps_158m = np.array([10, 20, 30, 40, 50,
                 60, 70, 80, 90, 100])
smoe_158m = np.array([38.42, 39.26, 40.05, 40.44, 40.79,
                 40.72, 40.99, 41.40, 41.43, 41.35])

deepseek_158m = np.array([38.11, 39.21, 40.04, 40.66, 40.94,
                     40.95, 40.94, 41.61, 41.53, 41.61])

deepseek_shared_158m = np.array([38.53, 39.47, 40.11, 40.45, 40.61,
                        40.73, 40.94, 41.35, 41.47, 41.54])

# Data: steps (in thousands) and corresponding percentage values for each method.
steps_679m = np.array([20, 40, 60, 80, 100, 120, 140, 160, 180, 200,
                 220, 240, 260, 280, 300, 320, 340, 360, 380, 400])
smoe_679m = np.array([41.90, 43.67, 44.22, 44.77, 45.49, 45.94, 45.97, 46.25, 46.71, 46.86,
                 46.80, 47.15, 47.33, 47.67, 47.18, 47.43, 48.01, 47.84, 47.89, 47.71])
deepseek_679m = np.array([42.07, 43.71, 44.49, 45.00, 45.54, 46.10, 46.00, 46.43, 46.83, 47.23,
                     47.16, 47.17, 47.51, 47.83, 47.74, 48.01, 48.34, 47.94, 48.30, 48.29])
deepseek_shared_679m = np.array([42.13, 43.74, 44.66, 44.58, 45.49, 46.00, 45.99, 46.07, 46.73,46.87, 
                            46.93, 47.03, 47.08, 47.58, 47.45, 47.73, 48.16, 47.87, 48.10, 47.91])

fig, axs = plt.subplots(1, 4, figsize=(20, 4.5), sharex=False, sharey=False)

# Set shared labels
fig.text(0.5, 0.17, 'Training Steps (k)', ha='center', fontsize=25, weight='bold')
fig.text(0.04, 0.7, 'Avg Performance (%)', va='center', rotation='vertical', fontsize=25, weight='bold')

xlims = [(7.5, 102.5), (7.5, 102.5), (0, 410), (0, 410)]
xticks = [np.arange(20, 101, 40)] * 2 + [np.arange(80, 401, 160)] * 2
ylims = [(38, 42), (38, 42), (41, 49), (41, 49)]
yticks = [np.arange(38, 43, 1)] * 2  + [np.arange(41, 50, 2)] * 2

# For legend collection
lines = []
labels = []

for i, ax in enumerate(axs):
    if i == 0:
        l1, = ax.plot(steps_158m, smoe_158m, linewidth=4, color='#4bc0d8', marker='o', markersize=6)
        l2, = ax.plot(steps_158m, deepseek_158m, linewidth=4, color='#e85b9c', marker='s', markersize=6)
        lines.extend([l1, l2])
        labels.extend(["SMoE", "DeepSeek"])
        # ax.set_title("158M", fontsize=25, weight='bold')
    elif i == 1:
        l3, = ax.plot(steps_158m, smoe_158m, linewidth=4, color='#4bc0d8', marker='o', markersize=6)
        l4, = ax.plot(steps_158m, deepseek_shared_158m, linewidth=4, color='#313465', marker='h', markersize=6)
        lines.extend([l4])
        labels.extend(["DeepSeek Shared Only"])
        # ax.set_title("158M", fontsize=25, weight='bold')
    elif i == 2:
        ax.plot(steps_679m, smoe_679m, linewidth=4, color='#4bc0d8', marker='o', markersize=6)
        ax.plot(steps_679m, deepseek_679m, linewidth=4, color='#e85b9c', marker='s', markersize=6)
        # ax.set_title("679M", fontsize=25, weight='bold')
    elif i == 3:
        ax.plot(steps_679m, smoe_679m, linewidth=4, color='#4bc0d8', marker='o', markersize=6)
        ax.plot(steps_679m, deepseek_shared_679m, linewidth=4, color='#313465', marker='h', markersize=6)
        # ax.set_title("679M", fontsize=25, weight='bold')
    
    ax.set_xlim(xlims[i])
    ax.set_xticks(xticks[i])
    ax.set_ylim(ylims[i])
    ax.set_yticks(yticks[i])
    
    ax.xaxis.set_tick_params(labelsize=25)
    ax.yaxis.set_tick_params(labelsize=25)
    
    ax.spines['right'].set_visible(False)
    ax.spines['top'].set_visible(False)
    
# Create a global legend in the right
fig.legend(lines, labels, loc='lower center', ncol=4, fontsize=20)

plt.tight_layout(rect=[0.06, 0.2, 1, 1.2])  # adjust bottom for legend space
plt.savefig('/home/<USER>/moeut_training_code/paper/deepseek/results/performances/lm/lm_performance.pdf', format='pdf', bbox_inches='tight')
plt.show()

colors = {
    "smoe": "#4bc0d8",
    "deepseek": "#e85b9c",
    "smoe_norm": "#f1b921",
    "deepseek_shared": "#313465",
}

steps = np.array([520, 1040, 1560, 2080, 2600, 3120, 3640, 4160, 4680, 5200,
                 5720, 6240, 6760, 7280, 7800, 8320, 8840, 9360, 9880, 10400]) 

smoe = np.array([50.53, 50.41, 49.89, 50.59, 50.83, 51.35, 51.53, 51.55, 51.14, 51.20,
                 51.30, 51.45, 51.54, 51.66, 51.47, 51.50, 51.51, 51.49, 51.52, 51.47])
smoe_sigmoid = np.array([49.51, 50.05, 50.10, 50.78, 50.68, 50.73, 50.88, 50.86, 50.88, 50.77,
                         51.25, 51.22, 51.25, 51.31, 51.44, 51.45, 51.53, 51.32, 51.40, 51.49])

deepseek = np.array([50.23, 49.99, 49.65, 50.25, 50.91, 51.11, 51.19, 51.03, 50.82, 51.06,
                     51.44, 51.34, 51.34, 51.60, 51.49, 51.72, 51.79, 51.85, 51.78, 51.75])

deepseek_shared = np.array([50.24, 49.92, 50.20, 50.32, 50.89, 51.23, 51.00, 51.05, 51.04, 51.09,
                   51.38, 51.26, 51.54, 51.59, 51.54, 51.65, 51.68, 51.59, 51.58, 51.41])

fig, axs = plt.subplots(1, 2, figsize=(20, 6), sharex=False, sharey=False)

# Set shared labels
fig.text(0.55, 0.0, 'Training Steps (k)', ha='center', fontsize=25, weight='bold')
fig.text(0.04, 0.57, 'Avg Performance (%)', va='center', rotation='vertical', fontsize=25, weight='bold')

xlims = [(0, 11), (0, 11)]
ylims = [(49.3, 52.0), (49.3, 52.0)]
xticks = [np.arange(0, 11, 2)] * 2
yticks = [np.arange(49.5, 52.5, 0.5)] * 2

# For legend collection
lines = []
labels = []

for i, ax in enumerate(axs):
    if i == 0:
        l1, = ax.plot(steps / 1000, smoe, linewidth=4, color='#4bc0d8', marker='o', markersize=6)
        l2, = ax.plot(steps / 1000, smoe_sigmoid, linewidth=4, color='#f1b921', marker='o', markersize=6)
        l3, = ax.plot(steps / 1000, deepseek_shared, linewidth=4, color='#313465', marker='h', markersize=6)
        l4, = ax.plot(steps / 1000, deepseek, linewidth=4, color='#e85b9c', marker='s', markersize=6)
        lines.extend([l1, l2, l3, l4])
        labels.extend(["SMoE", "SMoE Sigmoid", "DeepSeek-V2", "DeepSeek-V3"])
        # ax.set_title("SMoE and DeepSeek-V3", fontsize=25, weight='bold')
    elif i == 1:
        l3, = ax.plot(steps / 1000, smoe, linewidth=4, color='#4bc0d8', marker='o', markersize=6)
        l4, = ax.plot(steps / 1000, smoe_sigmoid, linewidth=4, color='#f1b921', marker='h', markersize=6)
        # lines.extend([l4])
        # # labels.extend(["DeepSeek-V2"])
        # ax.set_title("SMoE and DeepSeek-V2", fontsize=25, weight='bold')

    ax.set_xticks(xticks[i])
    ax.set_xlim(xlims[i])
    
    if i == 0: 
        ax.set_yticks(yticks[i])
        ax.set_ylim(ylims[i])
        ax.yaxis.set_tick_params(labelsize=25)
    if i == 1:
        # set no y ticks
        ax.set_yticks([])
        ax.set_ylim(ylims[i])
        
    ax.xaxis.set_tick_params(labelsize=25)
    
    
    ax.spines['right'].set_visible(False)
    ax.spines['top'].set_visible(False)

# Create a global legend in the right
fig.legend(lines, labels, loc='upper center', ncol=4, fontsize=20, bbox_to_anchor=(0.55, 1.0),)

plt.tight_layout(rect=[0.06, 0.03, 1, 0.93])  # leave top 8% blank
plt.savefig('/home/<USER>/moeut_training_code/paper/deepseek/results/performances/vlm/vlm_performance_pair.pdf', format='pdf', bbox_inches='tight')
plt.show()

colors = {
    "smoe": "#4bc0d8",
    "deepseek": "#e85b9c",
    "smoe_norm": "#f1b921",
    "deepseek_shared": "#313465",
}

steps = np.array([520, 1040, 1560, 2080, 2600, 3120, 3640, 4160, 4680, 5200,
                 5720, 6240, 6760, 7280, 7800, 8320, 8840, 9360, 9880, 10400]) 

smoe = np.array([50.53, 50.41, 49.89, 50.59, 50.83, 51.35, 51.53, 51.55, 51.14, 51.20,
                 51.30, 51.45, 51.54, 51.66, 51.47, 51.50, 51.51, 51.49, 51.52, 51.47])

deepseek = np.array([50.23, 49.99, 49.65, 50.25, 50.91, 51.11, 51.19, 51.03, 50.82, 51.06,
                     51.44, 51.34, 51.34, 51.60, 51.49, 51.72, 51.79, 51.85, 51.78, 51.75])

deepseek_shared = np.array([50.24, 49.92, 50.20, 50.32, 50.89, 51.23, 51.00, 51.05, 51.04, 51.09,
                   51.38, 51.26, 51.54, 51.59, 51.54, 51.65, 51.68, 51.59, 51.58, 51.41])

fig, axs = plt.subplots(1, 3, figsize=(20, 4), sharex=False, sharey=False)

# Set shared labels
fig.text(0.55, 0.0, 'Training Steps (k)', ha='center', fontsize=25, weight='bold')
fig.text(0.04, 0.5, 'Avg Performance (%)', va='center', rotation='vertical', fontsize=25, weight='bold')

xlims = [(0, 11), (0, 11), (0, 11)]
ylims = [(49.6, 51.9), (49.6, 51.9), (49.6, 51.9)]
xticks = [np.arange(0, 11, 2)] * 3
yticks = [np.arange(49.5, 52.5, 0.5)] * 3

# For legend collection
lines = []
labels = []

for i, ax in enumerate(axs):
    if i == 0:
        l1, = ax.plot(steps / 1000, smoe, linewidth=4, color='#4bc0d8', marker='o', markersize=6)
        l2, = ax.plot(steps / 1000, deepseek, linewidth=4, color='#e85b9c', marker='s', markersize=6)
        lines.extend([l1, l2])
        labels.extend(["SMoE", "DeepSeek-V3"])
        # ax.set_title("SMoE and DeepSeek-V3", fontsize=25, weight='bold')
    elif i == 1:
        l3, = ax.plot(steps / 1000, smoe, linewidth=4, color='#4bc0d8', marker='o', markersize=6)
        l4, = ax.plot(steps / 1000, deepseek_shared, linewidth=4, color='#313465', marker='h', markersize=6)
        lines.extend([l4])
        labels.extend(["DeepSeek-V2"])
        # ax.set_title("SMoE and DeepSeek-V2", fontsize=25, weight='bold')
    elif i == 2:
        # deepseek_shared and deepseek
        l7, = ax.plot(steps / 1000, smoe, linewidth=2, color='#4bc0d8', marker='o', markersize=4, linestyle='--')
        l5, = ax.plot(steps / 1000, deepseek_shared, linewidth=4, color='#313465', marker='h', markersize=6)
        l6, = ax.plot(steps / 1000, deepseek, linewidth=4, color='#e85b9c', marker='s', markersize=6)
        # ax.set_title("DeepSeek-V2 and DeepSeek-V3", fontsize=25, weight='bold')

    ax.set_xticks(xticks[i])
    ax.set_xlim(xlims[i])
    
    if i == 0: 
        ax.set_yticks(yticks[i])
        ax.set_ylim(ylims[i])
        ax.yaxis.set_tick_params(labelsize=25)
    else:
        # set no y ticks
        ax.set_yticks([])
        ax.set_ylim(ylims[i])
    
    ax.xaxis.set_tick_params(labelsize=25)
    
    ax.spines['right'].set_visible(False)
    ax.spines['top'].set_visible(False)

# Create a global legend in the right
fig.legend(lines, labels, loc='upper center', ncol=4, fontsize=20, bbox_to_anchor=(0.55, 1.05),)

# plt.tight_layout(rect=[0.06, 0.25, 1, 0.92])  # adjust bottom for legend space
plt.tight_layout(rect=[0.06, 0.03, 1, 0.9])  # leave top 8% blank
plt.savefig('/home/<USER>/moeut_training_code/paper/deepseek/results/performances/vlm/vlm_performance.pdf', format='pdf', bbox_inches='tight')
plt.show()

import json
import numpy as np
import matplotlib.pyplot as plt
from tensorboard.backend.event_processing.event_accumulator import EventAccumulator

event_smoe = EventAccumulator('/home/<USER>/moeut_training_code/tensorboard/158m/smoe/tensorboard')
event_smoe.Reload()  # Load the events
event_sigmoid = EventAccumulator('/home/<USER>/moeut_training_code/tensorboard/158m/sigmoid/tensorboard')
event_sigmoid.Reload()  # Load the events
event_shared = EventAccumulator('/home/<USER>/moeut_training_code/tensorboard/158m/shared/tensorboard')
event_shared.Reload()  # Load the events
event_deepseek = EventAccumulator('/home/<USER>/moeut_training_code/tensorboard/158m/deepseek/tensorboard')
event_deepseek.Reload()  # Load the events

steps_training = list(range(40, 10001, 40))
steps_validation = [10, 20, 30, 40, 50, 60, 70, 80, 90, 100]
steps = steps_validation

import numpy as np

data = {
    "Steps": np.array([10, 20, 30, 40, 50, 60, 70, 80, 90, 100]),
    "SMoE": {
        "PPL": np.array([20.62, 17.83, 16.53, 15.74, 15.05, 14.57, 14.20, 13.88, 13.72, 13.63]),
        "LAMBADA": np.array([16.23, 19.35, 20.03, 21.51, 23.72, 23.39, 24.59, 24.94, 24.63, 25.27]),
        "BLiMP": np.array([71.96, 73.21, 75.04, 75.59, 75.78, 76.93, 76.43, 77.31, 77.60, 77.71]),
        "CBT": np.array([78.05, 80.06, 81.93, 82.18, 82.61, 82.99, 83.34, 83.82, 84.11, 84.18]),
        "HellaSwag": np.array([26.93, 27.94, 28.14, 28.42, 28.62, 29.02, 28.72, 29.27, 29.34, 29.43]),
        "PIQA": np.array([54.95, 55.93, 57.83, 56.64, 56.80, 57.67, 57.73, 57.02, 58.05, 57.94]),
        "ARC_Challenge": np.array([21.03, 20.34, 21.63, 21.03, 20.94, 20.00, 20.43, 20.77, 20.77, 21.20]),
        "RACE": np.array([28.84, 29.07, 29.33, 30.20, 29.91, 29.91, 30.16, 30.66, 30.44, 30.11]),
        "SIQA": np.array([37.05, 36.23, 36.54, 36.28, 36.59, 35.57, 35.93, 35.88, 35.93, 35.62]),
        "CommonSenseQA": np.array([23.59, 22.85, 23.26, 25.31, 24.49, 24.73, 25.80, 24.98, 25.39, 24.65]),
        "Average": np.array([39.85, 40.56, 41.53, 41.91, 42.16, 42.25, 42.57, 42.74, 42.92, 42.90]),
    },
    
    "DeepSeek-V3": {
        "PPL": np.array([20.50, 17.67, 16.31, 15.55, 14.84, 14.37, 14.00, 13.69, 13.52, 13.42]),
        "LAMBADA": np.array([15.99, 18.69, 20.71, 22.28, 23.76, 22.55, 22.61, 25.43, 25.27, 25.49]),
        "BLiMP": np.array([71.32, 73.42, 75.04, 75.15, 75.26, 76.37, 76.52, 77.31, 77.16, 77.20]),
        "CBT": np.array([78.02, 79.89, 81.68, 82.64, 82.83, 83.42, 83.91, 84.02, 84.39, 84.40]),
        "HellaSwag": np.array([27.11, 27.54, 28.19, 28.28, 28.29, 29.04, 28.91, 28.99, 28.85, 29.38]),
        "PIQA": np.array([55.06, 55.88, 56.69, 58.05, 58.16, 57.73, 57.67, 58.54, 58.54, 59.14]),
        "ARC_Challenge": np.array([19.83, 19.91, 20.94, 20.60, 21.37, 21.89, 21.63, 21.80, 21.20, 21.63]),
        "RACE": np.array([28.36, 29.15, 29.12, 29.29, 30.22, 29.97, 30.55, 31.23, 30.88, 30.60]),
        "SIQA": np.array([35.98, 35.88, 36.18, 36.75, 36.59, 35.36, 35.06, 35.77, 35.93, 35.57]),
        "CommonSenseQA": np.array([24.24, 24.16, 24.00, 25.96, 25.31, 25.80, 26.13, 26.04, 25.88, 25.47]),
        "Average": np.array([39.55, 40.50, 41.40, 42.11, 42.42, 42.46, 42.55, 43.24, 43.12, 43.21]),
    },
    
    "SMoE Sigmoid Gating": {
        "PPL": np.array([20.63, 17.87, 16.57, 15.72, 15.05, 14.54, 14.18, 13.87, 13.70, 13.61]),
        "LAMBADA": np.array([15.78, 18.85, 20.48, 21.06, 22.85, 23.00, 24.84, 24.81, 24.46, 25.43]),
        "BLiMP": np.array([72.07, 74.89, 74.14, 75.94, 75.92, 76.40, 76.65, 77.08, 77.56, 77.38]),
        "CBT": np.array([77.92, 80.05, 81.90, 82.25, 82.84, 83.34, 83.61, 83.96, 84.76, 84.23]),
        "HellaSwag": np.array([27.24, 27.41, 27.82, 28.25, 28.56, 28.57, 28.69, 29.06, 29.09, 29.13]),
        "PIQA": np.array([55.11, 55.44, 56.96, 57.24, 56.80, 57.73, 58.60, 58.49, 58.22, 58.92]),
        "ARC_Challenge": np.array([20.26, 20.00, 21.12, 19.66, 21.12, 21.80, 20.60, 21.29, 21.03, 21.37]),
        "RACE": np.array([29.37, 29.28, 29.92, 31.34, 29.95, 30.65, 30.57, 30.89, 30.58, 31.05]),
        "SIQA": np.array([34.65, 35.36, 35.47, 36.18, 35.72, 35.57, 35.36, 35.62, 35.72, 34.90]),
        "CommonSenseQA": np.array([23.59, 22.93, 24.08, 24.82, 24.73, 24.82, 24.57, 24.90, 25.14, 24.90]),
        "Average": np.array([39.55, 40.47, 41.32, 41.86, 42.05, 42.43, 42.61, 42.90, 42.95, 43.04]),
    },
    
    "DeepSeek-V2": {
        "PPL": np.array([20.44, 17.65, 16.39, 15.59, 14.93, 14.44, 14.06, 13.76, 13.60, 13.49]),
        "LAMBADA": np.array([16.46, 18.73, 20.54, 22.85, 22.38, 21.60, 23.33, 24.44, 24.84, 25.29]),
        "BLiMP": np.array([71.89, 73.35, 74.19, 74.50, 76.14, 76.06, 76.51, 76.97, 76.70, 77.37]),
        "CBT": np.array([78.60, 80.20, 81.55, 82.29, 83.09, 83.69, 83.60, 83.89, 84.31, 84.33]),
        "HellaSwag": np.array([26.74, 27.41, 27.79, 27.95, 28.68, 28.46, 28.79, 29.13, 29.11, 29.38]),
        "PIQA": np.array([55.55, 57.40, 57.78, 58.22, 58.54, 58.11, 60.17, 59.52, 59.30, 60.17]),
        "ARC_Challenge": np.array([20.34, 20.43, 21.29, 20.09, 20.86, 20.69, 20.34, 21.03, 20.77, 20.52]),
        "RACE": np.array([28.85, 29.42, 29.93, 29.81, 29.75, 30.56, 30.74, 30.90, 30.92, 31.02]),
        "SIQA": np.array([35.52, 36.90, 35.93, 37.21, 35.36, 35.36, 34.65, 35.72, 35.36, 35.57]),
        "CommonSenseQA": np.array([24.73, 24.08, 24.49, 24.57, 24.90, 24.82, 24.49, 24.08, 24.41, 24.98]),
        "Average": np.array([39.85, 40.88, 41.50, 41.94, 42.19, 42.15, 42.51, 42.85, 42.86, 43.18]),
    },
}

def filter_duplicate_steps(events, get_steps):
    seen_steps = set()
    filtered = []
    for event in events:
        if event.step not in get_steps:
            continue

        if event.step not in seen_steps:
            filtered.append(event.value)
            seen_steps.add(event.step)
            
    return filtered


log_info_training = {
    "Training Loss": {
        "smoe": filter_duplicate_steps(event_smoe.Scalars('train/loss'), steps_training),
        "sigmoid": filter_duplicate_steps(event_sigmoid.Scalars('train/loss'), steps_training),
        "shared": filter_duplicate_steps(event_shared.Scalars('train/loss'), steps_training),
        "deepseek": filter_duplicate_steps(event_deepseek.Scalars('train/loss'), steps_training)
    },
    "Balancing Loss": {
        "smoe": filter_duplicate_steps(event_smoe.Scalars('train/reg_loss/moe'), list(map(lambda x: x + 1, steps_training))),
        "sigmoid": filter_duplicate_steps(event_sigmoid.Scalars('train/reg_loss/moe'), list(map(lambda x: x + 1, steps_training))),
        "shared": filter_duplicate_steps(event_shared.Scalars('train/reg_loss/moe'), list(map(lambda x: x + 1, steps_training))),
        "deepseek": filter_duplicate_steps(event_deepseek.Scalars('train/reg_loss/moe'), list(map(lambda x: x + 1, steps_training)))
    },
}

log_info_validation = {
    "Validation Loss": {
        "smoe": filter_duplicate_steps(event_smoe.Scalars('validation/val/loss'), list(map(lambda x: x * 1000, steps_validation))),
        "sigmoid": filter_duplicate_steps(event_sigmoid.Scalars('validation/val/loss'), list(map(lambda x: x * 1000, steps_validation))),
        "shared": filter_duplicate_steps(event_shared.Scalars('validation/val/loss'), list(map(lambda x: x * 1000, steps_validation))),
        "deepseek": filter_duplicate_steps(event_deepseek.Scalars('validation/val/loss'), list(map(lambda x: x * 1000, steps_validation)))
    }
}

colors = {
    "smoe": "#4bc0d8",
    "deepseek": "#e85b9c",
    "smoe_norm": "#f1b921",
    "deepseek_shared": "#313465",
}

fig, axs = plt.subplots(4, 3, figsize=(20, 16), sharex=False, sharey=False)

all_metrics = ["PPL"]

# Set shared labels
fig.text(0.55, 0.0, 'Training Steps (k)', ha='center', fontsize=25, weight='bold')
fig.text(0.04, 0.5, 'Performance', va='center', rotation='vertical', fontsize=25, weight='bold')

xticks = [20, 40, 60, 80, 100]

# For legend collection
lines = []
labels = []

for i, ax in enumerate(axs):
    if i == 0:
        # apply for log_info metrics
        for j, metric in enumerate(log_info_training):
            ax[j].plot(list(map(lambda x: (x/100), steps_training)), log_info_training[metric]["smoe"], linewidth=2, color='#4bc0d8', marker='o', markersize=0)
            ax[j].plot(list(map(lambda x: (x/100), steps_training)), log_info_training[metric]["deepseek"], linewidth=2, color='#e85b9c', marker='s', markersize=0)
            ax[j].plot(list(map(lambda x: (x/100), steps_training)), log_info_training[metric]["sigmoid"], linewidth=2, color='#f1b921', marker='^', markersize=0)
            ax[j].plot(list(map(lambda x: (x/100), steps_training)), log_info_training[metric]["shared"], linewidth=2, color='#313465', marker='h', markersize=0)
            
            ax[j].set_title(metric, fontsize=20, weight='bold')
        
        ax[2].plot(steps_validation, log_info_validation["Validation Loss"]["smoe"], linewidth=3, color='#4bc0d8', marker='o', markersize=5)
        ax[2].plot(steps_validation, log_info_validation["Validation Loss"]["deepseek"], linewidth=3, color='#e85b9c', marker='s', markersize=5)
        ax[2].plot(steps_validation, log_info_validation["Validation Loss"]["sigmoid"], linewidth=3, color='#f1b921', marker='^', markersize=5)
        ax[2].plot(steps_validation, log_info_validation["Validation Loss"]["shared"], linewidth=3, color='#313465', marker='h', markersize=5)
        ax[2].set_title("Validation Loss", fontsize=20, weight='bold')
    
    if i == 1:
        # apply for "LAMBADA", "BLiMP", "CBT" metrics
        for j, metric in enumerate(["LAMBADA", "BLiMP", "CBT"]):
            l1, = ax[j].plot(steps, data["SMoE"][metric], linewidth=3, color='#4bc0d8', marker='o', markersize=5)
            l2, = ax[j].plot(steps, data["DeepSeek-V3"][metric], linewidth=3, color='#e85b9c', marker='s', markersize=5)
            l3, = ax[j].plot(steps, data["SMoE Sigmoid Gating"][metric], linewidth=3, color='#f1b921', marker='^', markersize=5)
            l4, = ax[j].plot(steps, data["DeepSeek-V2"][metric], linewidth=3, color='#313465', marker='h', markersize=5)
            if j == 0:
                lines.extend([l1, l2, l3, l4])
                labels.extend(["SMoE", "DeepSeek-V3", "SMoE Sigmoid Gating", "DeepSeek-V2"])
                
            ax[j].set_title(metric, fontsize=20, weight='bold')
                
    elif i == 2:
        # apply for "HellaSwag", "PIQA", "ARC_Challenge" metrics
        for j, metric in enumerate(["HellaSwag", "PIQA", "ARC_Challenge"]):
            l1, = ax[j].plot(steps, data["SMoE"][metric], linewidth=3, color='#4bc0d8', marker='o', markersize=5)
            l2, = ax[j].plot(steps, data["DeepSeek-V3"][metric], linewidth=3, color='#e85b9c', marker='s', markersize=5)
            l3, = ax[j].plot(steps, data["SMoE Sigmoid Gating"][metric], linewidth=3, color='#f1b921', marker='^', markersize=5)
            l4, = ax[j].plot(steps, data["DeepSeek-V2"][metric], linewidth=3, color='#313465', marker='h', markersize=5)
            
            # Set title by metric name
            ax[j].set_title(metric, fontsize=20, weight='bold')
            
            
    elif i == 3:
        # apply for "RACE", "SIQA", "CommonSenseQA" metrics
        for j, metric in enumerate(["RACE", "SIQA", "CommonSenseQA"]):
            l1, = ax[j].plot(steps, data["SMoE"][metric], linewidth=3, color='#4bc0d8', marker='o', markersize=5)
            l2, = ax[j].plot(steps, data["DeepSeek-V3"][metric], linewidth=3, color='#e85b9c', marker='s', markersize=5)
            l3, = ax[j].plot(steps, data["SMoE Sigmoid Gating"][metric], linewidth=3, color='#f1b921', marker='^', markersize=5)
            l4, = ax[j].plot(steps, data["DeepSeek-V2"][metric], linewidth=3, color='#313465', marker='h', markersize=5)
            
            ax[j].set_title(metric, fontsize=20, weight='bold')

    ax[0].set_xticks(xticks)
    ax[1].set_xticks(xticks)
    ax[2].set_xticks(xticks)
        
    ax[0].xaxis.set_tick_params(labelsize=20)
    ax[1].xaxis.set_tick_params(labelsize=20)
    ax[2].xaxis.set_tick_params(labelsize=20)
    
    ax[0].yaxis.set_tick_params(labelsize=20)
    ax[1].yaxis.set_tick_params(labelsize=20)
    ax[2].yaxis.set_tick_params(labelsize=20)
        
    ax[0].spines['right'].set_visible(False)
    ax[1].spines['right'].set_visible(False)
    ax[2].spines['right'].set_visible(False)
    
    ax[0].spines['top'].set_visible(False)
    ax[1].spines['top'].set_visible(False)
    ax[2].spines['top'].set_visible(False)
        

# Create a global legend in the right
fig.legend(lines, labels, loc='lower center', ncol=4, fontsize=20, bbox_to_anchor=(0.55, 1.0))

# plt.tight_layout(rect=[0.062, 0.06, 1, 1.3])  # adjust bottom for legend space
plt.tight_layout(rect=[0.06, 0.01, 1, 1])  # leave top 8% blank
plt.savefig('/home/<USER>/moeut_training_code/paper/deepseek/results/each_benchmarks/lm_performance_each_benrchmark_158m.pdf', format='pdf', bbox_inches='tight')
plt.show()

event_smoe = EventAccumulator('/home/<USER>/moeut_training_code/tensorboard/679m/smoe/tensorboard')
event_smoe.Reload()  # Load the events

step_training = list(set([x.step for x in event_smoe.Scalars('train/loss')]))
step_training.sort()
training_loss = list(set([x.value for x in event_smoe.Scalars('train/loss')]))

plt.figure(figsize=(16, 8))
plt.plot(training_loss)

event_smoe = EventAccumulator('/home/<USER>/moeut_training_code/tensorboard/679m/smoe/tensorboard')
event_smoe.Reload()  # Load the events
event_sigmoid = EventAccumulator('/home/<USER>/moeut_training_code/tensorboard/679m/sigmoid/tensorboard')
event_sigmoid.Reload()  # Load the events
event_shared = EventAccumulator('/home/<USER>/moeut_training_code/tensorboard/679m/shared/tensorboard')
event_shared.Reload()  # Load the events
event_deepseek = EventAccumulator('/home/<USER>/moeut_training_code/tensorboard/679m/deepseek/tensorboard')
event_deepseek.Reload()  # Load the events

steps_validation =[20, 40, 60, 80, 100, 120, 140, 160, 180, 200,
                 220, 240, 260, 280, 300, 320, 340, 360, 380, 400]


steps_training = {
    "smoe": list(set([x.step for x in event_smoe.Scalars('train/loss')])),
    "sigmoid": list(set([x.step for x in event_sigmoid.Scalars('train/loss')])),
    "shared": list(set([x.step for x in event_shared.Scalars('train/loss')])),
    "deepseek": list(set([x.step for x in event_deepseek.Scalars('train/loss')]))
}
for key in steps_training: 
    steps_training[key].sort()
    steps = steps_training[key]
    mod_steps = []
    for i in range(len(steps)):
        if i % 4 == 0:
            mod_steps.append(steps[i])
    steps_training[key] = mod_steps
    

steps_moe_loss = {
    "smoe": list(set([x.step for x in event_smoe.Scalars('train/reg_loss/moe')])),
    "sigmoid": list(set([x.step for x in event_sigmoid.Scalars('train/reg_loss/moe')])),
    "shared": list(set([x.step for x in event_shared.Scalars('train/reg_loss/moe')])),
    "deepseek": list(set([x.step for x in event_deepseek.Scalars('train/reg_loss/moe')]))
}
for key in steps_moe_loss:
    steps_moe_loss[key].sort()
    steps = steps_moe_loss[key]
    mod_steps = []
    for i in range(len(steps)):
        if i % 4 == 0:
            mod_steps.append(steps[i])
    steps_moe_loss[key] = mod_steps

import numpy as np

data = {
    "Steps": np.array([20, 40, 60, 80, 100, 120, 140, 160, 180, 200,
                       220, 240, 260, 280, 300, 320, 340, 360, 380, 400]),
    "SMoE": {
        "PPL": np.array([15.20, 13.34, 12.45, 11.87, 11.51, 11.23, 11.00, 10.81, 10.58, 10.42,
                         10.27, 10.12, 10.01, 9.90, 9.80, 9.72, 9.64, 9.58, 9.54, 9.51]),
        "LAMBADA": np.array([21.39, 29.10, 28.40, 29.58, 32.53, 34.49, 32.74, 33.15, 35.23, 34.14,
                             35.13, 35.11, 36.88, 38.53, 35.95, 37.46, 39.09, 38.08, 38.53, 37.13]),
        "BLiMP": np.array([75.50, 75.89, 76.53, 78.91, 79.32, 79.83, 79.28, 79.39, 79.65, 80.39,
                           79.26, 79.79, 80.38, 80.13, 80.13, 80.16, 80.69, 80.21, 80.12, 80.47]),
        "CBT": np.array([83.18, 85.36, 86.37, 87.08, 87.53, 87.91, 88.02, 88.45, 88.37, 88.78,
                         88.99, 89.06, 89.41, 89.86, 89.54, 89.73, 89.83, 89.78, 89.88, 89.83]),
        "HellaSwag": np.array([28.69, 30.21, 31.40, 31.94, 32.64, 33.13, 33.76, 33.98, 34.73, 35.17,
                               35.70, 35.59, 35.93, 36.41, 36.63, 37.22, 37.01, 37.50, 37.31, 37.49]),
        "PIQA": np.array([57.62, 59.03, 59.90, 59.58, 61.53, 61.86, 61.48, 62.73, 63.28, 63.71,
                          63.33, 63.38, 64.09, 64.25, 63.33, 64.25, 64.85, 64.91, 64.31, 64.36]),
        "ARC_Challenge": np.array([19.83, 20.94, 22.15, 21.97, 21.97, 22.15, 23.35, 23.18, 22.66, 22.58,
                                   22.40, 24.29, 23.00, 23.18, 22.32, 22.75, 23.43, 23.18, 23.61, 23.09]),
        "RACE": np.array([29.82, 30.71, 30.99, 32.14, 31.59, 31.45, 31.44, 31.26, 32.95, 32.36,
                          32.29, 32.69, 32.71, 32.56, 32.53, 32.48, 32.79, 32.74, 32.96, 33.03]),
        "SIQA": np.array([35.41, 36.13, 36.03, 36.28, 36.03, 36.49, 37.00, 37.41, 37.05, 37.72,
                          36.85, 37.72, 37.05, 37.67, 37.31, 36.80, 37.62, 37.56, 37.62, 37.41]),
        "CommonSenseQA": np.array([25.63, 25.63, 26.21, 25.47, 26.29, 26.13, 26.62, 26.70, 26.45, 26.86,
                                   27.27, 26.70, 26.54, 26.45, 26.86, 26.04, 26.78, 26.62, 26.70, 26.54]),
        "Average": np.array([41.90, 43.67, 44.22, 44.77, 45.49, 45.94, 45.97, 46.25, 46.71, 46.86,
                             46.80, 47.15, 47.33, 47.67, 47.18, 47.43, 48.01, 47.84, 47.89, 47.71]),
    },
    "DeepSeek-V3": {
        "PPL": np.array([15.13, 13.25, 12.41, 11.84, 11.47, 11.19, 10.96, 10.77, 10.56, 10.39,
                         10.24, 10.11, 10.00, 9.88, 9.79, 9.70, 9.61, 9.56, 9.53, 9.49]),
        "LAMBADA": np.array([21.27, 29.06, 26.61, 28.84, 31.50, 34.41, 32.03, 32.80, 34.39, 33.93,
                             36.02, 36.04, 36.47, 37.44, 35.13, 36.96, 38.72, 37.48, 38.20, 36.88]),
        "BLiMP": np.array([74.97, 77.07, 78.33, 78.83, 79.07, 79.94, 79.83, 80.06, 80.19, 80.96,
                           80.44, 80.23, 80.59, 80.79, 80.65, 81.09, 81.16, 80.67, 81.31, 81.28]),
        "CBT": np.array([83.15, 85.38, 85.98, 86.61, 87.09, 87.39, 87.88, 88.31, 88.65, 88.47,
                         88.70, 89.06, 89.26, 89.45, 89.40, 89.51, 89.52, 89.47, 89.49, 89.65]),
        "HellaSwag": np.array([28.93, 30.46, 31.46, 32.03, 32.36, 33.44, 34.04, 34.29, 34.79, 35.50,
                               35.29, 35.67, 35.92, 36.11, 36.50, 36.80, 36.97, 37.03, 37.45, 37.32]),
        "PIQA": np.array([57.18, 58.54, 60.07, 61.81, 61.75, 61.86, 62.46, 62.89, 62.24, 63.60,
                          63.71, 63.82, 64.20, 64.64, 65.13, 64.91, 65.34, 65.29, 65.89, 65.72]),
        "ARC_Challenge": np.array([21.03, 20.26, 22.32, 21.55, 22.23, 22.49, 21.80, 23.00, 22.92, 23.00,
                                   23.00, 22.32, 22.40, 23.86, 24.38, 23.43, 23.35, 22.40, 22.83, 23.95]),
        "RACE": np.array([30.28, 30.59, 31.35, 32.68, 32.08, 32.35, 31.97, 32.19, 32.22, 32.86,
                          32.75, 33.00, 32.98, 33.09, 33.15, 32.93, 32.99, 33.19, 33.63, 33.12]),
        "SIQA": np.array([36.44, 36.28, 37.87, 36.64, 37.51, 36.64, 37.36, 37.67, 37.31, 38.69,
                          36.75, 37.26, 38.33, 37.92, 37.72, 38.84, 38.89, 38.28, 38.18, 38.59]),
        "CommonSenseQA": np.array([25.39, 25.72, 26.37, 26.04, 26.29, 26.37, 26.62, 26.62, 28.75, 28.09,
                                   27.76, 27.11, 27.44, 27.19, 27.60, 27.60, 28.09, 27.60, 27.76, 28.09]),
        "Average": np.array([42.07, 43.71, 44.49, 45.00, 45.54, 46.10, 46.00, 46.43, 46.83, 47.23,
                             47.16, 47.17, 47.51, 47.83, 47.74, 48.01, 48.34, 47.94, 48.30, 48.29]),
    },
    "SMoE Sigmoid Gating": {
        "PPL": np.array([15.11, 13.24, 12.36, 11.81, 11.43, 11.16, 10.93, 10.73, 10.53, 10.38,
                         10.22, 10.09, 9.98, 9.85, 9.74, 9.66, 9.60, 9.54, 9.50, 9.46]),
        "LAMBADA": np.array([22.44, 29.89, 28.96, 27.91, 31.75, 33.72, 33.39, 31.64, 34.39, 35.42,
                             34.98, 35.99, 35.48, 37.60, 34.92, 37.46, 39.38, 37.99, 37.93, 37.56]),
        "BLiMP": np.array([75.01, 77.14, 78.17, 78.68, 80.19, 80.24, 79.80, 79.90, 80.24, 81.33,
                           80.71, 80.81, 81.27, 81.00, 80.64, 81.06, 81.55, 81.09, 81.21, 81.08]),
        "CBT": np.array([83.43, 85.48, 85.96, 87.15, 87.13, 87.32, 87.80, 88.43, 88.75, 88.73,
                         88.84, 89.28, 89.13, 89.52, 89.27, 89.57, 89.27, 89.57, 89.63, 89.57]),
        "HellaSwag": np.array([28.77, 30.34, 31.39, 31.90, 32.69, 33.52, 33.97, 34.77, 35.01, 35.27,
                               35.51, 35.94, 36.06, 36.53, 36.71, 37.16, 36.80, 37.18, 37.06, 37.52]),
        "PIQA": np.array([57.89, 59.30, 60.17, 62.08, 61.86, 62.51, 61.97, 62.57, 64.64, 62.46,
                          63.33, 63.06, 63.33, 63.93, 63.33, 64.20, 65.07, 65.13, 64.96, 64.91]),
        "ARC_Challenge": np.array([21.97, 21.20, 20.69, 22.06, 22.49, 22.40, 22.66, 23.26, 22.75, 23.69,
                                   23.26, 24.38, 23.35, 22.83, 22.92, 23.26, 22.92, 23.95, 23.26, 23.09]),
        "RACE": np.array([29.91, 29.80, 31.07, 31.46, 31.00, 31.63, 31.35, 31.60, 31.87, 31.52,
                          32.24, 32.33, 31.72, 32.08, 32.17, 32.68, 32.54, 32.57, 32.72, 32.68]),
        "SIQA": np.array([36.69, 35.52, 36.64, 36.34, 36.08, 36.80, 38.13, 36.90, 37.87, 38.02,
                          36.69, 37.41, 37.26, 37.15, 36.90, 37.82, 37.56, 37.56, 38.23, 37.67]),
        "CommonSenseQA": np.array([24.16, 25.31, 26.37, 27.11, 26.78, 27.11, 27.11, 26.54, 27.60, 27.60,
                                   28.01, 27.68, 27.27, 28.09, 28.26, 29.07, 28.99, 28.83, 29.07, 28.50]),
        "Average": np.array([42.25, 43.78, 44.38, 44.97, 45.55, 46.14, 46.24, 46.18, 47.01, 47.12,
                             47.06, 47.43, 47.21, 47.64, 47.23, 48.03, 48.23, 48.21, 48.23, 48.06]),
    },
    
    "DeepSeek-V2": {
        "PPL": np.array([15.21, 13.26, 12.43, 11.90, 11.52, 11.23, 11.00, 10.82, 10.59, 10.44,
                         10.30, 10.15, 10.03, 9.92, 9.82, 9.74, 9.66, 9.61, 9.56, 9.52]),
        "LAMBADA": np.array([21.84, 28.44, 27.14, 27.83, 31.60, 33.62, 31.79, 31.89, 35.27, 33.77,
                             34.47, 35.19, 35.64, 36.76, 36.10, 36.96, 39.34, 37.71, 38.18, 37.11]),
        "BLiMP": np.array([74.94, 77.17, 78.66, 78.59, 79.65, 79.94, 80.23, 80.39, 79.87, 80.50,
                           80.16, 80.71, 80.76, 80.76, 80.40, 80.80, 81.08, 80.80, 81.10, 80.98]),
        "CBT": np.array([83.26, 85.25, 86.56, 87.01, 86.94, 87.49, 88.18, 88.14, 88.48, 88.73,
                         88.74, 89.12, 89.27, 89.72, 89.57, 89.73, 89.98, 89.68, 89.84, 89.93]),
        "HellaSwag": np.array([28.92, 29.92, 31.44, 32.06, 32.53, 33.14, 33.50, 33.65, 34.61, 34.83,
                               35.09, 35.47, 35.52, 35.89, 36.24, 36.44, 36.65, 36.76, 36.85, 37.14]),
        "PIQA": np.array([57.51, 60.01, 60.07, 60.17, 61.97, 62.24, 62.08, 61.75, 62.95, 62.84,
                          63.22, 62.79, 63.17, 63.60, 62.89, 64.31, 64.20, 64.09, 64.53, 64.36]),
        "ARC_Challenge": np.array([20.94, 20.86, 22.83, 22.75, 22.49, 23.00, 22.75, 22.92, 23.69, 24.46,
                                   23.61, 23.43, 22.49, 23.52, 23.86, 23.95, 24.03, 24.55, 24.55, 24.21]),
        "RACE": np.array([30.18, 30.65, 31.41, 31.60, 31.69, 31.89, 32.36, 32.40, 32.06, 32.05,
                          32.96, 33.01, 32.38, 32.93, 32.79, 32.80, 33.06, 33.04, 33.23, 33.17]),
        "SIQA": np.array([36.23, 36.44, 37.77, 35.88, 36.28, 35.98, 35.88, 36.80, 36.95, 37.21,
                          37.26, 36.75, 37.36, 37.46, 37.15, 37.56, 37.67, 37.21, 37.41, 36.95]),
        "CommonSenseQA": np.array([25.39, 24.90, 26.04, 25.31, 26.29, 26.70, 27.19, 26.70, 26.70, 27.44,
                                   26.86, 26.78, 27.11, 27.60, 28.09, 27.03, 27.44, 27.03, 27.19, 27.35]),
        "Average": np.array([42.13, 43.74, 44.66, 44.58, 45.49, 46.00, 45.99, 46.07, 46.73, 46.87,
                             46.93, 47.03, 47.08, 47.58, 47.45, 47.73, 48.16, 47.87, 48.10, 47.91]),
    },
}


def filter_duplicate_steps(events, get_steps):
    seen_steps = set()
    filtered = []
    for event in events:
        if event.step not in get_steps:
            continue

        if event.step not in seen_steps:
            filtered.append(event.value)
            seen_steps.add(event.step)
            
    return filtered


log_info_training = {
    "Training Loss": {
        "smoe": filter_duplicate_steps(event_smoe.Scalars('train/loss'), steps_training["smoe"]),
        "sigmoid": filter_duplicate_steps(event_sigmoid.Scalars('train/loss'), steps_training["sigmoid"]),
        "shared": filter_duplicate_steps(event_shared.Scalars('train/loss'), steps_training["shared"]),
        "deepseek": filter_duplicate_steps(event_deepseek.Scalars('train/loss'), steps_training["deepseek"])
    },
    "Balancing Loss": {
        "smoe": filter_duplicate_steps(event_smoe.Scalars('train/reg_loss/moe'), steps_moe_loss["smoe"]),
        "sigmoid": filter_duplicate_steps(event_sigmoid.Scalars('train/reg_loss/moe'), steps_moe_loss["sigmoid"]),
        "shared": filter_duplicate_steps(event_shared.Scalars('train/reg_loss/moe'), steps_moe_loss["shared"]),
        "deepseek": filter_duplicate_steps(event_deepseek.Scalars('train/reg_loss/moe'), steps_moe_loss["deepseek"])
    },
}

log_info_validation = {
    "Validation Loss": {
        "smoe": filter_duplicate_steps(event_smoe.Scalars('validation/val/loss'), list(map(lambda x: x * 1000, steps_validation))),
        "sigmoid": filter_duplicate_steps(event_sigmoid.Scalars('validation/val/loss'), list(map(lambda x: x * 1000, steps_validation))),
        "shared": filter_duplicate_steps(event_shared.Scalars('validation/val/loss'), list(map(lambda x: x * 1000, steps_validation))),
        "deepseek": filter_duplicate_steps(event_deepseek.Scalars('validation/val/loss'), list(map(lambda x: x * 1000, steps_validation)))
    }
}

colors = {
    "smoe": "#4bc0d8",
    "deepseek": "#e85b9c",
    "smoe_norm": "#f1b921",
    "deepseek_shared": "#313465",
}

fig, axs = plt.subplots(4, 3, figsize=(20, 16), sharex=False, sharey=False)

all_metrics = ["PPL"]

# Set shared labels
fig.text(0.55, 0.0, 'Training Steps (k)', ha='center', fontsize=25, weight='bold')
fig.text(0.04, 0.5, 'Performance', va='center', rotation='vertical', fontsize=25, weight='bold')

xticks = [80, 160, 240, 320, 400]

# For legend collection
lines = []
labels = []
steps = steps_validation

for i, ax in enumerate(axs):
    if i == 0:
        # apply for log_info metrics
        for j, metric in enumerate(log_info_training):
            applied_steps = steps_training if metric == "Training Loss" else steps_moe_loss
            ax[j].plot(list(map(lambda x: (x/1000), applied_steps["smoe"])), log_info_training[metric]["smoe"], linewidth=2, color='#4bc0d8', marker='o', markersize=0)
            ax[j].plot(list(map(lambda x: (x/1000), applied_steps["deepseek"])), log_info_training[metric]["deepseek"], linewidth=2, color='#e85b9c', marker='s', markersize=0)
            ax[j].plot(list(map(lambda x: (x/1000), applied_steps["sigmoid"])), log_info_training[metric]["sigmoid"], linewidth=2, color='#f1b921', marker='^', markersize=0)
            ax[j].plot(list(map(lambda x: (x/1000), applied_steps["shared"])), log_info_training[metric]["shared"], linewidth=2, color='#313465', marker='h', markersize=0)
            
            ax[j].set_title(metric, fontsize=20, weight='bold')
        
        ax[2].plot(steps_validation, log_info_validation["Validation Loss"]["smoe"], linewidth=3, color='#4bc0d8', marker='o', markersize=5)
        ax[2].plot(steps_validation, log_info_validation["Validation Loss"]["deepseek"], linewidth=3, color='#e85b9c', marker='s', markersize=5)
        ax[2].plot(steps_validation, log_info_validation["Validation Loss"]["sigmoid"], linewidth=3, color='#f1b921', marker='^', markersize=5)
        ax[2].plot(steps_validation, log_info_validation["Validation Loss"]["shared"], linewidth=3, color='#313465', marker='h', markersize=5)
        ax[2].set_title("Validation Loss", fontsize=20, weight='bold')
    
    if i == 1:
        # apply for "LAMBADA", "BLiMP", "CBT" metrics
        for j, metric in enumerate(["LAMBADA", "BLiMP", "CBT"]):
            l1, = ax[j].plot(steps, data["SMoE"][metric], linewidth=3, color='#4bc0d8', marker='o', markersize=5)
            l2, = ax[j].plot(steps, data["DeepSeek-V3"][metric], linewidth=3, color='#e85b9c', marker='s', markersize=5)
            l3, = ax[j].plot(steps, data["SMoE Sigmoid Gating"][metric], linewidth=3, color='#f1b921', marker='^', markersize=5)
            l4, = ax[j].plot(steps, data["DeepSeek-V2"][metric], linewidth=3, color='#313465', marker='h', markersize=5)
            if j == 0:
                lines.extend([l1, l2, l3, l4])
                labels.extend(["SMoE", "DeepSeek-V3", "SMoE Sigmoid Gating", "DeepSeek-V2"])
                
            ax[j].set_title(metric, fontsize=20, weight='bold')
                
    elif i == 2:
        # apply for "HellaSwag", "PIQA", "ARC_Challenge" metrics
        for j, metric in enumerate(["HellaSwag", "PIQA", "ARC_Challenge"]):
            l1, = ax[j].plot(steps, data["SMoE"][metric], linewidth=3, color='#4bc0d8', marker='o', markersize=5)
            l2, = ax[j].plot(steps, data["DeepSeek-V3"][metric], linewidth=3, color='#e85b9c', marker='s', markersize=5)
            l3, = ax[j].plot(steps, data["SMoE Sigmoid Gating"][metric], linewidth=3, color='#f1b921', marker='^', markersize=5)
            l4, = ax[j].plot(steps, data["DeepSeek-V2"][metric], linewidth=3, color='#313465', marker='h', markersize=5)
            
            # Set title by metric name
            ax[j].set_title(metric, fontsize=20, weight='bold')
            
            
    elif i == 3:
        # apply for "RACE", "SIQA", "CommonSenseQA" metrics
        for j, metric in enumerate(["RACE", "SIQA", "CommonSenseQA"]):
            l1, = ax[j].plot(steps, data["SMoE"][metric], linewidth=3, color='#4bc0d8', marker='o', markersize=5)
            l2, = ax[j].plot(steps, data["DeepSeek-V3"][metric], linewidth=3, color='#e85b9c', marker='s', markersize=5)
            l3, = ax[j].plot(steps, data["SMoE Sigmoid Gating"][metric], linewidth=3, color='#f1b921', marker='^', markersize=5)
            l4, = ax[j].plot(steps, data["DeepSeek-V2"][metric], linewidth=3, color='#313465', marker='h', markersize=5)
            
            ax[j].set_title(metric, fontsize=20, weight='bold')

    ax[0].set_xticks(xticks)
    ax[1].set_xticks(xticks)
    ax[2].set_xticks(xticks)
        
    ax[0].xaxis.set_tick_params(labelsize=20)
    ax[1].xaxis.set_tick_params(labelsize=20)
    ax[2].xaxis.set_tick_params(labelsize=20)
    
    ax[0].yaxis.set_tick_params(labelsize=20)
    ax[1].yaxis.set_tick_params(labelsize=20)
    ax[2].yaxis.set_tick_params(labelsize=20)
        
    ax[0].spines['right'].set_visible(False)
    ax[1].spines['right'].set_visible(False)
    ax[2].spines['right'].set_visible(False)
    
    ax[0].spines['top'].set_visible(False)
    ax[1].spines['top'].set_visible(False)
    ax[2].spines['top'].set_visible(False)
        

# Create a global legend in the right
fig.legend(lines, labels, loc='lower center', ncol=4, fontsize=20, bbox_to_anchor=(0.55, 1.0))

# plt.tight_layout(rect=[0.062, 0.06, 1, 1.3])  # adjust bottom for legend space
plt.tight_layout(rect=[0.06, 0.01, 1, 1])  # leave top 8% blank
plt.savefig('/home/<USER>/moeut_training_code/paper/deepseek/results/each_benchmarks/lm_performance_each_benrchmark_679m.pdf', format='pdf', bbox_inches='tight')
plt.show()

with open("/home/<USER>/moeut_training_code/tensorboard/vlm_training_states/deepseek/trainer_state.json", "r") as f:
    logs_smoe = json.load(f)
with open("/home/<USER>/moeut_training_code/tensorboard/vlm_training_states/sigmoid/trainer_state.json", "r") as f:
    logs_sigmoid = json.load(f)
with open("/home/<USER>/moeut_training_code/tensorboard/vlm_training_states/shared/trainer_state.json", "r") as f:
    logs_shared = json.load(f)
with open("/home/<USER>/moeut_training_code/tensorboard/vlm_training_states/deepseek/trainer_state.json", "r") as f:
    logs_deepseek = json.load(f)

steps_training = np.array(range(1, 10395, 20))

# extract the loss, language_loss and auxiliary_loss_mlp
def extract_logs_vlm(logs, get_steps: list):
    loss = [log["loss"] for log in logs["log_history"][:-1] if log["step"] in get_steps]
    language_loss = [log["language_loss"] for log in logs["log_history"][:-1] if log["step"] in get_steps]
    auxiliary_loss_mlp = [log["auxiliary_loss_mlp"] for log in logs["log_history"][:-1] if log["step"] in get_steps]
    return loss, language_loss, auxiliary_loss_mlp

loss, language_loss, auxiliary_loss_mlp = {}, {}, {}
loss["smoe"], language_loss["smoe"], auxiliary_loss_mlp["smoe"] = extract_logs_vlm(logs_smoe, steps_training)
loss["sigmoid"], language_loss["sigmoid"], auxiliary_loss_mlp["sigmoid"] = extract_logs_vlm(logs_sigmoid, steps_training)
loss["shared"], language_loss["shared"], auxiliary_loss_mlp["shared"] = extract_logs_vlm(logs_shared, steps_training)
loss["deepseek"], language_loss["deepseek"], auxiliary_loss_mlp["deepseek"] = extract_logs_vlm(logs_deepseek, steps_training)


loss_info = {
    "Training Loss": loss,
    "Language Loss": language_loss,
    "Balancing Loss": auxiliary_loss_mlp
}

data = {
    "Steps": np.array([520, 1040, 1560, 2080, 2600, 3120, 3640, 4160, 4680, 5200,
                       5720, 6240, 6760, 7280, 7800, 8320, 8840, 9360, 9880, 10400]),
    "SMoE": {
        "AI2D": np.array([64.05, 63.67, 63.70, 64.93, 64.09, 64.31, 64.70, 65.41, 65.41, 65.19,
                        64.67, 64.15, 64.73, 64.44, 64.90, 64.86, 64.96, 65.09, 64.99, 64.90]),
        "MMStar": np.array([42.14, 40.54, 40.39, 42.31, 40.45, 42.27, 43.30, 42.09, 42.42, 41.72,
                            42.38, 42.19, 42.57, 42.16, 41.73, 42.14, 41.53, 42.19, 42.02, 41.66]),
        "POPE": np.array([85.10, 85.42, 83.71, 85.14, 85.43, 86.20, 84.91, 85.48, 84.48, 85.82,
                        84.72, 84.69, 85.73, 86.02, 85.57, 85.56, 85.86, 85.77, 85.62, 85.67]),
        "ScienceQA": np.array([82.32, 81.77, 82.17, 81.75, 81.51, 81.37, 81.84, 81.80, 81.91, 81.70,
                            82.17, 81.80, 81.70, 81.54, 81.75, 81.63, 81.63, 81.68, 81.75, 81.61]),
        "TextVQA": np.array([38.01, 38.28, 38.02, 38.12, 39.68, 38.92, 39.28, 39.06, 39.29, 40.11,
                            39.83, 40.60, 40.79, 40.94, 40.53, 40.66, 40.76, 40.82, 40.89, 40.92]),
        "GQA": np.array([52.88, 54.77, 54.52, 56.07, 57.61, 57.18, 57.74, 58.79, 59.11, 59.02,
                        59.21, 59.86, 60.03, 60.41, 60.21, 59.99, 60.34, 60.18, 60.16, 60.19]),
        "MME-RealWorld-Lite": np.array([34.71, 32.00, 30.95, 30.64, 32.62, 35.23, 35.44, 34.34, 31.94, 31.79,
                                    32.57, 32.57, 31.79, 31.53, 31.84, 31.63, 31.84, 31.68, 31.84, 31.79]),
        "MMMU Pro Standard": np.array([24.34, 26.13, 24.68, 25.43, 25.20, 25.49, 25.90, 26.30, 25.55, 25.67,
                                        26.24, 26.30, 25.55, 26.30, 25.78, 26.47, 25.67, 25.72, 25.61, 25.61]),
        "OCRBench": np.array([31.20, 31.10, 30.90, 30.90, 30.90, 31.20, 30.70, 30.70, 30.10, 29.80,
                            29.90, 30.90, 31.00, 31.60, 30.90, 30.60, 31.00, 30.30, 30.80, 30.90]),
    },
    "DeepSeek-V3": {
        "AI2D": np.array([63.83, 63.86, 64.86, 64.15, 64.05, 64.38, 64.93, 65.12, 64.77, 64.96,
                        64.96, 64.48, 64.70, 64.80, 65.19, 65.22, 65.74, 65.41, 65.58, 65.45]),
        "MMStar": np.array([41.89, 39.93, 39.40, 40.24, 41.18, 41.39, 40.69, 41.00, 40.31, 40.64,
                            42.03, 42.09, 39.92, 41.48, 41.42, 41.22, 41.37, 41.81, 41.01, 41.40]),
        "POPE": np.array([84.46, 84.92, 83.29, 85.30, 86.22, 85.28, 85.78, 85.59, 84.92, 85.80,
                        84.53, 84.74, 85.64, 85.68, 85.60, 85.22, 86.21, 85.81, 85.44, 85.44]),
        "ScienceQA": np.array([81.80, 81.80, 81.82, 81.56, 81.66, 81.00, 81.63, 81.54, 81.94, 81.82,
                            81.82, 82.15, 82.06, 81.99, 82.01, 82.34, 81.96, 81.99, 81.91, 81.94]),
        "TextVQA": np.array([37.57, 38.57, 37.65, 37.34, 39.12, 39.24, 39.25, 39.04, 39.34, 39.98,
                            39.72, 40.33, 40.70, 40.51, 40.26, 40.47, 40.39, 40.98, 40.77, 40.69]),
        "GQA": np.array([52.95, 54.42, 54.19, 56.59, 56.85, 57.31, 57.45, 58.51, 58.48, 58.07,
                        58.60, 59.41, 59.73, 59.48, 59.56, 59.76, 59.92, 59.99, 60.01, 60.01]),
        "MME-RealWorld-Lite": np.array([33.40, 29.39, 29.34, 29.86, 32.93, 34.71, 34.45, 32.26, 31.53, 30.59,
                                    32.88, 31.84, 32.36, 32.67, 31.94, 32.57, 32.41, 32.15, 32.46, 32.20]),
        "MMMU Pro Standard": np.array([24.62, 26.24, 25.43, 25.67, 25.32, 26.13, 26.42, 26.53, 25.90, 26.71,
                                    27.23, 26.13, 25.95, 26.07, 25.90, 26.53, 25.84, 26.07, 26.13, 26.01]),
        "OCRBench": np.array([31.60, 30.80, 30.90, 31.50, 30.90, 30.60, 30.10, 29.70, 30.20, 31.00,
                            31.20, 30.90, 31.00, 31.70, 31.50, 32.10, 32.30, 32.40, 32.70, 32.60]),
    },
    "SMoE Sigmoid Gating": {
        "AI2D": np.array([62.73, 64.31, 63.12, 63.86, 63.31, 64.86, 65.06, 64.12, 63.44, 64.18,
                        64.57, 64.18, 64.12, 64.70, 64.41, 64.83, 64.73, 64.64, 64.67, 65.41]),
        "MMStar": np.array([40.77, 41.35, 41.70, 42.75, 41.71, 40.62, 40.86, 41.17, 42.39, 40.52,
                            41.44, 41.75, 40.86, 42.02, 41.98, 41.36, 41.65, 41.50, 42.00, 41.20]),
        "POPE": np.array([84.60, 84.17, 84.58, 85.91, 85.29, 85.08, 85.83, 84.91, 84.58, 85.31,
                        85.23, 85.27, 85.66, 85.90, 85.90, 85.82, 86.14, 85.88, 85.88, 85.68]),
        "ScienceQA": np.array([82.08, 81.80, 81.96, 82.10, 81.73, 81.77, 81.89, 82.22, 81.96, 82.08,
                            82.22, 82.34, 82.29, 82.06, 82.22, 82.08, 82.20, 82.17, 82.29, 81.96]),
        "TextVQA": np.array([38.34, 37.82, 37.52, 38.90, 38.47, 38.55, 38.79, 38.77, 37.90, 39.36,
                            39.68, 40.03, 40.39, 40.32, 40.37, 40.28, 40.47, 40.55, 40.50, 41.04]),
        "GQA": np.array([53.14, 54.71, 54.61, 56.50, 57.35, 57.46, 57.15, 58.43, 58.96, 58.44,
                        58.64, 59.60, 59.81, 60.03, 60.05, 60.11, 60.09, 60.06, 60.07, 60.07]),
        "MME-RealWorld-Lite": np.array([28.14, 29.96, 30.38, 30.48, 32.93, 32.62, 31.63, 31.89, 33.19, 30.69,
                                    32.15, 31.42, 31.79, 31.06, 30.90, 31.53, 31.74, 30.80, 30.85, 31.68]),
        "MMMU Pro Standard": np.array([25.32, 25.15, 25.61, 25.61, 25.15, 26.19, 26.24, 25.84, 24.91, 26.07,
                                    27.34, 26.36, 25.84, 26.01, 26.53, 26.47, 25.78, 25.61, 25.55, 25.95]),
        "OCRBench": np.array([30.50, 31.20, 31.40, 30.90, 30.20, 29.40, 30.50, 30.40, 30.60, 30.30,
                            30.00, 30.00, 30.50, 29.70, 30.60, 30.60, 31.00, 30.70, 30.80, 31.00]),
    },
    "DeepSeek-V2": {    
        "AI2D": np.array([63.80, 63.15, 64.05, 64.22, 64.02, 63.41, 64.31, 64.67, 64.31, 64.80,
                        65.22, 64.48, 64.86, 65.09, 64.90, 65.29, 65.58, 65.51, 65.58, 64.70]),
        "MMStar": np.array([41.84, 40.05, 42.38, 40.66, 42.32, 42.97, 41.42, 40.28, 40.54, 40.40,
                            40.99, 40.71, 41.00, 40.58, 41.29, 41.39, 41.39, 41.10, 41.00, 41.55]),
        "POPE": np.array([84.97, 85.07, 84.14, 84.91, 85.61, 85.22, 85.04, 85.91, 84.88, 85.77,
                        85.34, 84.64, 85.80, 86.11, 85.99, 85.69, 86.07, 85.82, 85.66, 85.80]),
        "ScienceQA": np.array([82.22, 81.87, 81.89, 82.22, 81.73, 81.56, 81.56, 81.82, 81.94, 81.94,
                            82.46, 82.32, 82.03, 81.94, 82.10, 81.96, 81.91, 82.01, 81.99, 82.20]),
        "TextVQA": np.array([37.27, 38.04, 37.87, 37.72, 38.72, 39.49, 39.48, 39.40, 38.95, 40.14,
                            39.79, 40.65, 41.46, 41.20, 40.98, 40.82, 40.95, 41.26, 41.14, 40.51]),
        "GQA": np.array([52.28, 54.53, 54.51, 56.15, 57.35, 57.78, 57.54, 58.66, 58.90, 58.41,
                        58.79, 59.60, 59.76, 60.10, 59.83, 59.94, 60.06, 60.16, 60.09, 60.15]),
        "MME-RealWorld-Lite": np.array([34.08, 30.54, 31.63, 30.48, 32.93, 34.76, 33.77, 32.36, 32.88, 32.15,
                                    32.88, 31.32, 32.20, 31.74, 31.79, 31.94, 32.15, 31.58, 31.63, 31.11]),
        "MMMU Pro Standard": np.array([25.43, 25.38, 25.09, 25.61, 25.15, 26.13, 24.97, 25.43, 25.38, 25.84,
                                    26.65, 26.65, 25.55, 26.19, 26.42, 26.42, 25.78, 25.84, 26.07, 25.72]),
        "OCRBench": np.array([30.30, 30.70, 30.20, 30.90, 30.20, 29.80, 30.90, 30.90, 31.60, 30.40,
                            30.30, 31.00, 31.20, 31.40, 30.60, 31.40, 31.20, 31.00, 31.10, 31.00]),
    }
}

colors = {
    "smoe": "#4bc0d8",
    "deepseek": "#e85b9c",
    "smoe_norm": "#f1b921",
    "deepseek_shared": "#313465",
}

fig, axs = plt.subplots(4, 3, figsize=(20, 16), sharex=False, sharey=False)

all_metrics = ["PPL"]

# Set shared labels
fig.text(0.55, 0.0, 'Training Steps (k)', ha='center', fontsize=25, weight='bold')
fig.text(0.04, 0.5, 'Performance', va='center', rotation='vertical', fontsize=25, weight='bold')

xticks = [2, 4, 6, 8, 10]

# For legend collection
lines = []
labels = []
steps = data["Steps"] / 1000

for i, ax in enumerate(axs):
    if i == 0:
        
        # apply for Log info
        for j, metric in enumerate(["Training Loss", "Language Loss", "Balancing Loss"]):
            ax[j].plot(steps_training/1000, loss_info[metric]["smoe"], linewidth=2, color='#4bc0d8', marker='o', markersize=0)
            ax[j].plot(steps_training/1000, loss_info[metric]["deepseek"], linewidth=2, color='#e85b9c', marker='s', markersize=0)
            ax[j].plot(steps_training/1000, loss_info[metric]["sigmoid"], linewidth=2, color='#f1b921', marker='^', markersize=0)
            ax[j].plot(steps_training/1000, loss_info[metric]["shared"], linewidth=2, color='#313465', marker='h', markersize=0)
            
            ax[j].set_title(metric, fontsize=20, weight='bold')
        
    if i == 1:
        # apply for "AI2D", "MMStar", "POPE"
        for j, metric in enumerate(["AI2D", "MMStar", "POPE"]):
            l1, = ax[j].plot(steps, data["SMoE"][metric], linewidth=3, color='#4bc0d8', marker='o', markersize=5)
            l2, = ax[j].plot(steps, data["DeepSeek-V3"][metric], linewidth=3, color='#e85b9c', marker='s', markersize=5)
            l3, = ax[j].plot(steps, data["SMoE Sigmoid Gating"][metric], linewidth=3, color='#f1b921', marker='^', markersize=5)
            l4, = ax[j].plot(steps, data["DeepSeek-V2"][metric], linewidth=3, color='#313465', marker='h', markersize=5)
            if j == 0:
                lines.extend([l1, l2, l3, l4])
                labels.extend(["SMoE", "DeepSeek-V3", "SMoE Sigmoid Gating", "DeepSeek-V2"])
                
            ax[j].set_title(metric, fontsize=20, weight='bold')
                
    elif i == 2:
        # apply for "ScienceQA", "TextVQA", "GQA"
        for j, metric in enumerate(["ScienceQA", "TextVQA", "GQA"]):
            l1, = ax[j].plot(steps, data["SMoE"][metric], linewidth=3, color='#4bc0d8', marker='o', markersize=5)
            l2, = ax[j].plot(steps, data["DeepSeek-V3"][metric], linewidth=3, color='#e85b9c', marker='s', markersize=5)
            l3, = ax[j].plot(steps, data["SMoE Sigmoid Gating"][metric], linewidth=3, color='#f1b921', marker='^', markersize=5)
            l4, = ax[j].plot(steps, data["DeepSeek-V2"][metric], linewidth=3, color='#313465', marker='h', markersize=5)
            
            # Set title by metric name
            ax[j].set_title(metric, fontsize=20, weight='bold')
            
            
    elif i == 3:
        # apply for "MME-RealWorld-Lite", "MMMU Pro Standard", "OCRBench"
        for j, metric in enumerate(["MME-RealWorld-Lite", "MMMU Pro Standard", "OCRBench"]):
            l1, = ax[j].plot(steps, data["SMoE"][metric], linewidth=3, color='#4bc0d8', marker='o', markersize=5)
            l2, = ax[j].plot(steps, data["DeepSeek-V3"][metric], linewidth=3, color='#e85b9c', marker='s', markersize=5)
            l3, = ax[j].plot(steps, data["SMoE Sigmoid Gating"][metric], linewidth=3, color='#f1b921', marker='^', markersize=5)
            l4, = ax[j].plot(steps, data["DeepSeek-V2"][metric], linewidth=3, color='#313465', marker='h', markersize=5)
            
            ax[j].set_title(metric, fontsize=20, weight='bold')

    ax[0].set_xticks(xticks)
    ax[1].set_xticks(xticks)
    ax[2].set_xticks(xticks)
        
    ax[0].xaxis.set_tick_params(labelsize=20)
    ax[1].xaxis.set_tick_params(labelsize=20)
    ax[2].xaxis.set_tick_params(labelsize=20)
    
    ax[0].yaxis.set_tick_params(labelsize=20)
    ax[1].yaxis.set_tick_params(labelsize=20)
    ax[2].yaxis.set_tick_params(labelsize=20)
        
    ax[0].spines['right'].set_visible(False)
    ax[1].spines['right'].set_visible(False)
    ax[2].spines['right'].set_visible(False)
    
    ax[0].spines['top'].set_visible(False)
    ax[1].spines['top'].set_visible(False)
    ax[2].spines['top'].set_visible(False)
        

# Create a global legend in the right
fig.legend(lines, labels, loc='lower center', ncol=4, fontsize=20, bbox_to_anchor=(0.55, 1.0))

# plt.tight_layout(rect=[0.062, 0.06, 1, 1.3])  # adjust bottom for legend space
plt.tight_layout(rect=[0.06, 0.01, 1, 1])  # leave top 8% blank
plt.savefig('/home/<USER>/moeut_training_code/paper/deepseek/results/each_benchmarks/vlm_performance_each_benrchmark.pdf', format='pdf', bbox_inches='tight')
plt.show()

import numpy as np

smoe_router = np.load("/home/<USER>/moeut_training_code/paper/deepseek/router_saturation_679M/smoe/model-400000.npy")